Loaded settings: 
Object
Topbar.js:39 Using logo: /uploads/styles/1736331866594-logo-1736331866594-adsad (1).png
Topbar.js:108 Admin status check: 
Object
(index):1718 Loaded models: 
Array(57)
(index):1835 Loaded templates from API: 
Array(4)
(index):1982 Selected template: 67e574467aaa0378712a06ae
(index):1992 Found template from API: 
Object
category
: 
"general"
createdAt
: 
"2025-03-27T15:52:38.602Z"
isPublic
: 
true
name
: 
"Vintage sci-fi Style"
prompt
: 
"Main Object/Character\n\nA [object] depicted in a vintage sci-fi illustration style. Around are some Dots, Lightning Bolt, Fire, Comet.\n\nPalette is strictly limited to [image-palette] to mimic retro screen-printed graphics. Deep shadows have a dense, ink-heavy style, and highlights are used sparingly.\n\nArt Style\n\n A combination of stylized engraving (xylography) and halftone techniques, featuring:\nHeavy comic-style inking and engraving textures. Also the style is [art].\n\n\nBlocky and structured shading with thick black outlines.\n\n(display TEXT Main Title: \"[text1]\" in the style extra-thick, bold block font with beveled edges.\ndisplay TEXT Top Phrase: \"[text2]\" in the style  small uppercase sans-serif.\ndisplay TEXT Subtext: \"[text3]\" in the style bold block, matching [text1].)\n\nHigh-contrast shading similar to vintage space exploration posters.\n\n\nClean yet expressive line work fitting the vintage sci-fi aesthetic.\n\n\n\n\n\nExtra Elements\nHelmet Reflection: Strong light streaks, cleanly curved to maintain the stylized print look.\n\n\nXylographic Inking & Halftones: Shadows created with fine halftones instead of smooth digital shading.\n\n\nCross-hatched Engraving Lines: Used inside darker areas for added texture.\n\n\nGeneral Look/Feel\nComposition: Circular badge design remains dominant.\n\n\nBackground Elements: Features two symmetrical celestial icons (satellite & orbiting planet).\n\n\nHalftone Gradient: Dots transition smoothly for a subtle vintage effect.\n\n\nAdditional Details: Minimal tiny sparks and stars around the badge to enhance the retro space theme.\n\nThe look is [feel]."
randomOptions
: 
{art: Array(12), elements: Array(20), feel: Array(20)}
template
: 
"Main Object/Character\n\nA [object] depicted in a vintage sci-fi illustration style. Around are some Dots, Lightning Bolt, Fire, Comet.\n\nPalette is strictly limited to [image-palette] to mimic retro screen-printed graphics. Deep shadows have a dense, ink-heavy style, and highlights are used sparingly.\n\nArt Style\n\n A combination of stylized engraving (xylography) and halftone techniques, featuring:\nHeavy comic-style inking and engraving textures. Also the style is [art].\n\n\nBlocky and structured shading with thick black outlines.\n\n(display TEXT Main Title: \"[text1]\" in the style extra-thick, bold block font with beveled edges.\ndisplay TEXT Top Phrase: \"[text2]\" in the style  small uppercase sans-serif.\ndisplay TEXT Subtext: \"[text3]\" in the style bold block, matching [text1].)\n\nHigh-contrast shading similar to vintage space exploration posters.\n\n\nClean yet expressive line work fitting the vintage sci-fi aesthetic.\n\n\n\n\n\nExtra Elements\nHelmet Reflection: Strong light streaks, cleanly curved to maintain the stylized print look.\n\n\nXylographic Inking & Halftones: Shadows created with fine halftones instead of smooth digital shading.\n\n\nCross-hatched Engraving Lines: Used inside darker areas for added texture.\n\n\nGeneral Look/Feel\nComposition: Circular badge design remains dominant.\n\n\nBackground Elements: Features two symmetrical celestial icons (satellite & orbiting planet).\n\n\nHalftone Gradient: Dots transition smoothly for a subtle vintage effect.\n\n\nAdditional Details: Minimal tiny sparks and stars around the badge to enhance the retro space theme.\n\nThe look is [feel]."
thumbnailUrl
: 
"/uploads/templates/template-1744019964966-320510175.png"
updatedAt
: 
"2025-06-05T19:06:47.252Z"
userId
: 
null
__v
: 
0
_id
: 
"67e574467aaa0378712a06ae"
[[Prototype]]
: 
Object
(index):2006 Template random options: 
Object
(index):1691 Image palette changed: 
Object
colors
: 
(3) ['#87ceeb', '#006994', '#ffa500']
description
: 
"sky blue, deep teal, and bright orange"
id
: 
"ocean-sunset"
name
: 
"Ocean Sunset"
[[Prototype]]
: 
Object
(index):1906 Using template: 
Object
id
: 
"67e574467aaa0378712a06ae"
prompt
: 
"Main Object/Character\n\nA [object] depicted in a vintage sci-fi illustration style. Around are some Dots, Lightning Bolt, Fire, Comet.\n\nPalette is strictly limited to [image-palette] to mimic retro screen-printed graphics. Deep shadows have a dense, ink-heavy style, and highlights are used sparingly.\n\nArt Style\n\n A combination of stylized engraving (xylography) and halftone techniques, featuring:\nHeavy comic-style inking and engraving textures. Also the style is [art].\n\n\nBlocky and structured shading with thick black outlines.\n\n(display TEXT Main Title: \"[text1]\" in the style extra-thick, bold block font with beveled edges.\ndisplay TEXT Top Phrase: \"[text2]\" in the style  small uppercase sans-serif.\ndisplay TEXT Subtext: \"[text3]\" in the style bold block, matching [text1].)\n\nHigh-contrast shading similar to vintage space exploration posters.\n\n\nClean yet expressive line work fitting the vintage sci-fi aesthetic.\n\n\n\n\n\nExtra Elements\nHelmet Reflection: Strong light streaks, cleanly curved to maintain the stylized print look.\n\n\nXylographic Inking & Halftones: Shadows created with fine halftones instead of smooth digital shading.\n\n\nCross-hatched Engraving Lines: Used inside darker areas for added texture.\n\n\nGeneral Look/Feel\nComposition: Circular badge design remains dominant.\n\n\nBackground Elements: Features two symmetrical celestial icons (satellite & orbiting planet).\n\n\nHalftone Gradient: Dots transition smoothly for a subtle vintage effect.\n\n\nAdditional Details: Minimal tiny sparks and stars around the badge to enhance the retro space theme.\n\nThe look is [feel]."
randomOptions
: 
{art: Array(12), elements: Array(20), feel: Array(20)}
[[Prototype]]
: 
Object
(index):1930 Generating with: 
Object
background
: 
"light"
imagePalette
: 
{id: 'ocean-sunset', name: 'Ocean Sunset', colors: Array(3), description: 'sky blue, deep teal, and bright orange'}
model
: 
"flux-yarn-art"
noText
: 
true
prompt
: 
"Cow"
randomOptions
: 
{art: Array(12), elements: Array(20), feel: Array(20)}
style
: 
undefined
templateId
: 
"67e574467aaa0378712a06ae"
templatePrompt
: 
"Main Object/Character\n\nA [object] depicted in a vintage sci-fi illustration style. Around are some Dots, Lightning Bolt, Fire, Comet.\n\nPalette is strictly limited to [image-palette] to mimic retro screen-printed graphics. Deep shadows have a dense, ink-heavy style, and highlights are used sparingly.\n\nArt Style\n\n A combination of stylized engraving (xylography) and halftone techniques, featuring:\nHeavy comic-style inking and engraving textures. Also the style is [art].\n\n\nBlocky and structured shading with thick black outlines.\n\n(display TEXT Main Title: \"[text1]\" in the style extra-thick, bold block font with beveled edges.\ndisplay TEXT Top Phrase: \"[text2]\" in the style  small uppercase sans-serif.\ndisplay TEXT Subtext: \"[text3]\" in the style bold block, matching [text1].)\n\nHigh-contrast shading similar to vintage space exploration posters.\n\n\nClean yet expressive line work fitting the vintage sci-fi aesthetic.\n\n\n\n\n\nExtra Elements\nHelmet Reflection: Strong light streaks, cleanly curved to maintain the stylized print look.\n\n\nXylographic Inking & Halftones: Shadows created with fine halftones instead of smooth digital shading.\n\n\nCross-hatched Engraving Lines: Used inside darker areas for added texture.\n\n\nGeneral Look/Feel\nComposition: Circular badge design remains dominant.\n\n\nBackground Elements: Features two symmetrical celestial icons (satellite & orbiting planet).\n\n\nHalftone Gradient: Dots transition smoothly for a subtle vintage effect.\n\n\nAdditional Details: Minimal tiny sparks and stars around the badge to enhance the retro space theme.\n\nThe look is [feel]."
text1
: 
""
text2
: 
""
text3
: 
""
theme
: 
""
[[Prototype]]
: 
Object
constructor
: 
ƒ Object()
hasOwnProperty
: 
ƒ hasOwnProperty()
isPrototypeOf
: 
ƒ isPrototypeOf()
propertyIsEnumerable
: 
ƒ propertyIsEnumerable()
toLocaleString
: 
ƒ toLocaleString()
toString
: 
ƒ toString()
valueOf
: 
ƒ valueOf()
__defineGetter__
: 
ƒ __defineGetter__()
__defineSetter__
: 
ƒ __defineSetter__()
__lookupGetter__
: 
ƒ __lookupGetter__()
__lookupSetter__
: 
ƒ __lookupSetter__()
__proto__
: 
(...)
get __proto__
: 
ƒ __proto__()
set __proto__
: 
ƒ __proto__()
GenerationCard.js:145 Admin status check: 
Object
isAdmin
: 
true
rawValue
: 
"true"
[[Prototype]]
: 
Object
constructor
: 
ƒ Object()
hasOwnProperty
: 
ƒ hasOwnProperty()
isPrototypeOf
: 
ƒ isPrototypeOf()
propertyIsEnumerable
: 
ƒ propertyIsEnumerable()
toLocaleString
: 
ƒ toLocaleString()
toString
: 
ƒ toString()
valueOf
: 
ƒ valueOf()
__defineGetter__
: 
ƒ __defineGetter__()
__defineSetter__
: 
ƒ __defineSetter__()
__lookupGetter__
: 
ƒ __lookupGetter__()
__lookupSetter__
: 
ƒ __lookupSetter__()
__proto__
: 
(...)
get __proto__
: 
ƒ __proto__()
set __proto__
: 
ƒ __proto__()
GenerationCard.js:145 Admin status check: 
Object
isAdmin
: 
true
rawValue
: 
"true"
[[Prototype]]
: 
Object
constructor
: 
ƒ Object()
hasOwnProperty
: 
ƒ hasOwnProperty()
isPrototypeOf
: 
ƒ isPrototypeOf()
propertyIsEnumerable
: 
ƒ propertyIsEnumerable()
toLocaleString
: 
ƒ toLocaleString()
toString
: 
ƒ toString()
valueOf
: 
ƒ valueOf()
__defineGetter__
: 
ƒ __defineGetter__()
__defineSetter__
: 
ƒ __defineSetter__()
__lookupGetter__
: 
ƒ __lookupGetter__()
__lookupSetter__
: 
ƒ __lookupSetter__()
__proto__
: 
(...)
get __proto__
: 
ƒ __proto__()
set __proto__
: 
ƒ __proto__()