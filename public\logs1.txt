Loaded settings: {_id: '677a8982348a8b5636fafc8c', __v: 0, createdAt: '2025-01-05T13:30:43.895Z', logoUrl: '/uploads/styles/1736331866594-logo-1736331866594-adsad (1).png', updatedAt: '2025-01-15T06:28:01.500Z', …}appName: "StickerLab"createdAt: "2025-01-05T13:30:43.895Z"logoUrl: "/uploads/styles/1736331866594-logo-1736331866594-adsad (1).png"mainTitle: "StickerLab App"updatedAt: "2025-01-15T06:28:01.500Z"useLogoInstead: true__v: 0_id: "677a8982348a8b5636fafc8c"[[Prototype]]: Object
Topbar.js:39 Using logo: /uploads/styles/1736331866594-logo-1736331866594-adsad (1).png
Topbar.js:108 Admin status check: {isAdmin: true}
prompt-templates.html:589 Templates loaded from database: (4) [{…}, {…}, {…}, {…}]
prompt-templates.html:752 Template loaded from database for editing: {_id: '67e574467aaa0378712a06ae', name: 'Vintage sci-fi Style', category: 'general', template: 'Main Object/Character\n\nA [object] depicted in a vi…hance the retro space theme.\n\nThe look is [feel].', thumbnailUrl: '/uploads/templates/template-1744019964966-320510175.png', …}
prompt-templates.html:665 Filling template form with data: {_id: '67e574467aaa0378712a06ae', name: 'Vintage sci-fi Style', category: 'general', template: 'Main Object/Character\n\nA [object] depicted in a vi…hance the retro space theme.\n\nThe look is [feel].', thumbnailUrl: '/uploads/templates/template-1744019964966-320510175.png', …}
prompt-templates.html:713 Template content set in editor: Main Object/Character

A [object] depicted in a vi...
prompt-templates.html:726 Random options set in editor: {art: Array(12), elements: Array(20), feel: Array(20)}
prompt-templates.html:458 Template original palette description updated: dark navy blue, muted burnt orange, and desaturated off-white
prompt-templates.html:833 Random options from editor for update: {art: Array(12), elements: Array(20), feel: Array(20)}
prompt-templates.html:856 Updating template with data: {name: 'Vintage sci-fi Style', category: 'general', template: 'Main Object/Character\n\nA [object] depicted in a vi…hance the retro space theme.\n\nThe look is [feel].', thumbnailUrl: '/uploads/templates/template-1744019964966-320510175.png', randomOptions: {…}, …}
prompt-templates.html:885 Template updated in database: {_id: '67e574467aaa0378712a06ae', name: 'Vintage sci-fi Style', category: 'general', template: 'Main Object/Character\n\nA [object] depicted in a vi…hance the retro space theme.\n\nThe look is [feel].', thumbnailUrl: '/uploads/templates/template-1744019964966-320510175.png', …}category: "general"createdAt: "2025-03-27T15:52:38.602Z"isPublic: truename: "Vintage sci-fi Style"randomOptions: {art: Array(12), elements: Array(20), feel: Array(20)}template: "Main Object/Character\n\nA [object] depicted in a vintage sci-fi illustration style. Around are some Dots, Lightning Bolt, Fire, Comet.\n\nPalette is strictly limited to [image-palette] to mimic retro screen-printed graphics. Deep shadows have a dense, ink-heavy style, and highlights are used sparingly.\n\nArt Style\n\n A combination of stylized engraving (xylography) and halftone techniques, featuring:\nHeavy comic-style inking and engraving textures. Also the style is [art].\n\n\nBlocky and structured shading with thick black outlines.\n\n(display TEXT Main Title: \"[text1]\" in the style extra-thick, bold block font with beveled edges.\ndisplay TEXT Top Phrase: \"[text2]\" in the style  small uppercase sans-serif.\ndisplay TEXT Subtext: \"[text3]\" in the style bold block, matching [text1].)\n\nHigh-contrast shading similar to vintage space exploration posters.\n\n\nClean yet expressive line work fitting the vintage sci-fi aesthetic.\n\n\n\n\n\nExtra Elements\nHelmet Reflection: Strong light streaks, cleanly curved to maintain the stylized print look.\n\n\nXylographic Inking & Halftones: Shadows created with fine halftones instead of smooth digital shading.\n\n\nCross-hatched Engraving Lines: Used inside darker areas for added texture.\n\n\nGeneral Look/Feel\nComposition: Circular badge design remains dominant.\n\n\nBackground Elements: Features two symmetrical celestial icons (satellite & orbiting planet).\n\n\nHalftone Gradient: Dots transition smoothly for a subtle vintage effect.\n\n\nAdditional Details: Minimal tiny sparks and stars around the badge to enhance the retro space theme.\n\nThe look is [feel]."thumbnailUrl: "/uploads/templates/template-1744019964966-320510175.png"updatedAt: "2025-06-05T23:24:20.258Z"userId: null__v: 0_id: "67e574467aaa0378712a06ae"[[Prototype]]: Object
prompt-templates.html:589 Templates loaded from database: (4) [{…}, {…}, {…}, {…}]
prompt-templates.html:752 Template loaded from database for editing: {_id: '67e574467aaa0378712a06ae', name: 'Vintage sci-fi Style', category: 'general', template: 'Main Object/Character\n\nA [object] depicted in a vi…hance the retro space theme.\n\nThe look is [feel].', thumbnailUrl: '/uploads/templates/template-1744019964966-320510175.png', …}
prompt-templates.html:665 Filling template form with data: {_id: '67e574467aaa0378712a06ae', name: 'Vintage sci-fi Style', category: 'general', template: 'Main Object/Character\n\nA [object] depicted in a vi…hance the retro space theme.\n\nThe look is [feel].', thumbnailUrl: '/uploads/templates/template-1744019964966-320510175.png', …}
prompt-templates.html:713 Template content set in editor: Main Object/Character

A [object] depicted in a vi...
prompt-templates.html:726 Random options set in editor: {art: Array(12), elements: Array(20), feel: Array(20)}