// Namespace setup
if (!window.editor) window.editor = {};
if (!window.editor.gridWarpHandler) window.editor.gridWarpHandler = null;
// Assuming mesh-warp-implementation.js defines activeMeshWarpHandler globally or attaches it to window.editor
let activeMeshWarpHandler = window.editor.meshWarpHandler; // Example, adjust if implementation differs
// Assuming grid-warp.js is loaded and defines GridWarp globally or similar
// let GridWarp = window.GridWarp; // Example, adjust if needed

// --- Editor Canvas Size Globals ---
const w = 1600; // Editor canvas width
const h = 1200; // Editor canvas height

// --- Canvas Background Color ---
let canvasBackgroundColor = '#ffffff'; // Default background color

// --- Setup ---
const canvas = document.getElementById("demo");
const canvasArea = document.getElementById("canvas-area");
const ctx = canvas.getContext("2d", { alpha: true });

// --- Artboard ---
let artboard = null;

// --- Zoom/Pan State ---
let scale = 1.0; let offsetX = 0; let offsetY = 0; const MIN_SCALE = 0.1; const MAX_SCALE = 10.0; const ZOOM_SENSITIVITY = 0.001; let isPanning = false; let panStartX, panStartY;

// --- Artboard Resize State ---
let isResizingArtboard = false;
let resizeCorner = null;

// --- Offscreen Canvases (Text Effects) ---
const os = document.createElement("canvas"); os.width = 2048; os.height = 2048; const octx = os.getContext("2d"); const tempWarpCanvas = document.createElement("canvas"); tempWarpCanvas.width = 2048; tempWarpCanvas.height = 2048; const tempWarpCtx = tempWarpCanvas.getContext("2d"); const letterCanvas = document.createElement("canvas"); letterCanvas.width = 1024; letterCanvas.height = 1024; const letterCtx = letterCanvas.getContext("2d");

// --- Controls References ---
const textControlsWrapper = document.getElementById('text-controls'); const iText = document.getElementById("iText"); const addEditTextBtn = document.getElementById("addEditTextBtn"); const deleteTextBtn = document.getElementById("deleteTextBtn"); const iTextColor = document.getElementById("iTextColor"); const iFontFamily = document.getElementById("iFontFamily"); const iBold = document.getElementById("iBold"); const iItalic = document.getElementById("iItalic"); const iFontSize = document.getElementById("iFontSize"); const iTextRotation = document.getElementById("iTextRotation"); const vFontSize = document.getElementById("vFontSize"); const vTextRotation = document.getElementById("vTextRotation"); const effectModeSelect = document.getElementById("effectMode"); const skewSlider = document.getElementById("skewSlider"); const skewYSlider = document.getElementById("skewYSlider"); const vSkew = document.getElementById("vSkew"); const vSkewY = document.getElementById("vSkewY"); const iCurve = document.getElementById("iCurve"); const iOffset = document.getElementById("iOffset"); const iHeight = document.getElementById("iHeight"); const iBottom = document.getElementById("iBottom"); const iTriangle = document.getElementById("iTriangle"); const iShiftCenter = document.getElementById("iShiftCenter"); const vCurve = document.getElementById("vCurve"); const vOffset = document.getElementById("vOffset"); const vHeight = document.getElementById("vHeight"); const vBottom = document.getElementById("vBottom"); const vShiftCenter = document.getElementById("vShiftCenter"); const iDiameter = document.getElementById("iDiameter"); const iKerning = document.getElementById("iKerning"); const iFlip = document.getElementById("iFlip"); const vDiameter = document.getElementById("vDiameter"); const vKerning = document.getElementById("vKerning"); const iCurveAmount = document.getElementById("iCurveAmount"); const iCurveKerning = document.getElementById("iCurveKerning"); const iCurveFlip = document.getElementById("iCurveFlip"); const vCurveAmount = document.getElementById("vCurveAmount"); const vCurveKerning = document.getElementById("vCurveKerning"); const shadowSelect = document.getElementById("shadow"); const shadowColorPicker = document.getElementById("shadowColor"); const shadowOffsetXSlider = document.getElementById("shadowOffsetX"); const shadowOffsetYSlider = document.getElementById("shadowOffsetY"); const shadowBlurSlider = document.getElementById("shadowBlur"); const vShadowOffsetX = document.getElementById("vShadowOffsetX"); const vShadowOffsetY = document.getElementById("vShadowOffsetY"); const vShadowBlur = document.getElementById("vShadowBlur"); const blockShadowColorPicker = document.getElementById("blockShadowColor"); const blockShadowOpacitySlider = document.getElementById("blockShadowOpacity"); const blockShadowOffsetSlider = document.getElementById("blockShadowOffset"); const blockShadowAngleSlider = document.getElementById("blockShadowAngle"); const blockShadowBlurSlider = document.getElementById("blockShadowBlur"); const vBlockShadowOpacity = document.getElementById("vBlockShadowOpacity"); const vBlockShadowOffset = document.getElementById("vBlockShadowOffset"); const vBlockShadowAngle = document.getElementById("vBlockShadowAngle"); const vBlockShadowBlur = document.getElementById("vBlockShadowBlur"); const lineShadowColorPicker = document.getElementById("lineShadowColor"); const lineShadowDistanceSlider = document.getElementById("lineShadowDistance"); const lineShadowAngleSlider = document.getElementById("lineShadowAngle"); const lineShadowThicknessSlider = document.getElementById("lineShadowThickness"); const vLineShadowDistance = document.getElementById("vLineShadowDistance"); const vLineShadowAngle = document.getElementById("vLineShadowAngle"); const vLineShadowThickness = document.getElementById("vLineShadowThickness"); const detailed3DPrimaryColorPicker = document.getElementById("detailed3DPrimaryColor"); const detailed3DPrimaryOpacitySlider = document.getElementById("detailed3DPrimaryOpacity"); const detailed3DOffsetSlider = document.getElementById("detailed3DOffset"); const detailed3DAngleSlider = document.getElementById("detailed3DAngle"); const detailed3DBlurSlider = document.getElementById("detailed3DBlur"); const detailed3DSecondaryColorPicker = document.getElementById("detailed3DSecondaryColor"); const detailed3DSecondaryOpacitySlider = document.getElementById("detailed3DSecondaryOpacity"); const detailed3DSecondaryWidthSlider = document.getElementById("detailed3DSecondaryWidth"); const detailed3DSecondaryOffsetXSlider = document.getElementById("detailed3DSecondaryOffsetX"); const detailed3DSecondaryOffsetYSlider = document.getElementById("detailed3DSecondaryOffsetY"); const vDetailed3DPrimaryOpacity = document.getElementById("vDetailed3DPrimaryOpacity"); const vDetailed3DOffset = document.getElementById("vDetailed3DOffset"); const vDetailed3DAngle = document.getElementById("vDetailed3DAngle"); const vDetailed3DBlur = document.getElementById("vDetailed3DBlur"); const vDetailed3DSecondaryOpacity = document.getElementById("vDetailed3DSecondaryOpacity"); const vDetailed3DSecondaryWidth = document.getElementById("vDetailed3DSecondaryWidth"); const vDetailed3DSecondaryOffsetX = document.getElementById("vDetailed3DSecondaryOffsetX"); const vDetailed3DSecondaryOffsetY = document.getElementById("vDetailed3DSecondaryOffsetY"); const strokeToggle = document.getElementById("strokeToggle"); const linesDecorationSelect = document.getElementById("linesDecoration"); const strokeWidthSlider = document.getElementById("strokeWidth"); const strokeColorPicker = document.getElementById("strokeColor"); const vStrokeWidth = document.getElementById("vStrokeWidth"); const hWeight = document.getElementById("hWeight"); const hDistance = document.getElementById("hDistance"); const hColor = document.getElementById("hColor"); const vHWeight = document.getElementById("vHWeight"); const vHDistance = document.getElementById("vHDistance"); const ccDistance = document.getElementById("ccDistance"); const ccColor = document.getElementById("ccColor"); const ccFillTop = document.getElementById("ccFillTop"); const ccFillBottom = document.getElementById("ccFillBottom"); const vCcDistance = document.getElementById("vCcDistance"); const oWeight = document.getElementById("oWeight"); const oDistance = document.getElementById("oDistance"); const oColor = document.getElementById("oColor"); const vOWeight = document.getElementById("vOWeight"); const vODistance = document.getElementById("vODistance"); const flcDistance = document.getElementById("flcDistance"); const flcFillTop = document.getElementById("flcFillTop"); const flcFillBottom = document.getElementById("flcFillBottom"); const flcColor = document.getElementById("flcColor"); const flcMaxWeight = document.getElementById("flcMaxWeight"); const flcSpacing = document.getElementById("flcSpacing"); const vFlcDistance = document.getElementById("vFlcDistance"); const vFlcMaxWeight = document.getElementById("vFlcMaxWeight"); const vFlcSpacing = document.getElementById("vFlcSpacing");
const addImageBtn = document.getElementById('addImageBtn'); const imageFileInput = document.getElementById('image-file-input'); const deleteImageBtn = document.getElementById('deleteImageBtn'); const imageControlsWrapper = document.getElementById('image-controls'); const noImageSelectedMsg = document.getElementById('no-image-selected-msg'); const iImageSize = document.getElementById('iImageSize'); const vImageSize = document.getElementById('vImageSize'); const iImageRotation = document.getElementById('iImageRotation'); const vImageRotation = document.getElementById('vImageRotation');
const zoomInBtn = document.getElementById('zoomInBtn'); const zoomOutBtn = document.getElementById('zoomOutBtn'); const zoomLevelSpan = document.getElementById('zoomLevel');
const moveForwardBtn = document.getElementById('moveForwardBtn'); const moveBackwardBtn = document.getElementById('moveBackwardBtn');
const toggleArtboardBtn = document.getElementById('toggleArtboardBtn');
const removeBgBtn = document.getElementById('removeBgBtn');
const saveTemplateBtn = document.getElementById('saveTemplateBtn');
const saveTextStyleBtn = document.getElementById('saveTextStyleBtn');
const gridWarpPaddingSlider = document.getElementById('gridWarpPadding');
const vGridWarpPadding = document.getElementById('vGridWarpPadding');
const gridWarpPaddingRow = document.getElementById('gridWarpPaddingRow');

// Sidebar Tabs & Panels
const sidebarTabs = document.querySelectorAll('.sidebar-tab');
const sidebarContents = document.querySelectorAll('.sidebar-content');
const propertyTabs = document.querySelectorAll('.property-tab');
const propertyPanels = document.querySelectorAll('.property-panel');

// --- State & Constants ---
let canvasObjects = []; let selectedObjectIndex = -1; let nextId = 0; let isDraggingObject = false; let dragStartX, dragStartY; let dragInitialObjectX, dragInitialObjectY; let dragInitialControlPoints = null;
const selectionBoxPadding = 5; const letterSourcePadding = 15;

// --- Object Factories ---
function createTextObject(options = {}) { const defaults = { id: nextId++, type: 'text', text: "TEXT", x: w / 2, y: h / 2, color: "#3b82f6", fontFamily: "Poppins", fontSize: 150, bold: true, italic: false, rotation: 0, isSelected: false, effectMode: 'normal', decorationMode: 'noDecoration', strokeMode: 'noStroke', shadowMode: 'noShadow', skewX: 0, skewY: 0, warpCurve: 100, warpOffset: 10, warpHeight: 100, warpBottom: 150, warpTriangle: false, warpShiftCenter: 100, circleDiameter: 600, circleKerning: 0, circleFlip: false, curveAmount: 40, curveKerning: 0, curveFlip: false, hLineWeight: 3, hLineDist: 7, hLineColor: "#0000FF", ccDist: 50, ccColor: "#00FF00", ccFillDir: "top", oLineWeight: 4, oLineDist: 3, oLineColor: "#0000FF", flcDist: 62, flcDir: 'top', flcColor: '#cccccc', flcWeight: 3, flcSpacing: 10, strokeWidth: 1, strokeColor: '#000000', shadowColor: '#000000', shadowOffsetX: 5, shadowOffsetY: 5, shadowBlur: 10, blockShadowColor: '#000000', blockShadowOpacity: 100, blockShadowOffset: 40, blockShadowAngle: -58, blockShadowBlur: 5, lineShadowColor: '#AAAAAA', lineShadowDist: 15, lineShadowAngle: -45, lineShadowThickness: 5, d3dPrimaryColor: '#000000', d3dPrimaryOpacity: 100, d3dOffset: 36, d3dAngle: -63, d3dBlur: 5, d3dSecondaryColor: '#00FF00', d3dSecondaryOpacity: 100, d3dSecondaryWidth: 0, d3dSecondaryOffsetX: -5, d3dSecondaryOffsetY: -5, gridPadding: 20 /* Added grid padding default */ }; return { ...defaults, ...options }; }
function createImageObject(imgElement, options = {}) { const defaults = { id: nextId++, type: 'image', image: imgElement, x: w / 2, y: h / 2, scale: 1.0, rotation: 0, isSelected: false, originalWidth: imgElement.naturalWidth, originalHeight: imgElement.naturalHeight, imageUrl: options.imageUrl || imgElement.src, generationId: null, isFromGeneration: false, backgroundRemoved: false }; return { ...defaults, ...options }; }

// --- Helpers ---
function hexToRgba(hex, alpha = 1) { let r = 0, g = 0, b = 0; if (hex.length === 4) { r = parseInt(hex[1] + hex[1], 16); g = parseInt(hex[2] + hex[2], 16); b = parseInt(hex[3] + hex[3], 16); } else if (hex.length === 7) { r = parseInt(hex[1] + hex[2], 16); g = parseInt(hex[3] + hex[4], 16); b = parseInt(hex[5] + hex[6], 16); } if (isNaN(r) || isNaN(g) || isNaN(b)) { console.warn(`Invalid hex: ${hex}`); return 'rgba(0,0,0,0)'; } return `rgba(${r},${g},${b},${alpha})`; }
function calculateOffset(distance, angleDegrees) { const angleRadians = angleDegrees * (Math.PI / 180); return { x: distance * Math.cos(angleRadians), y: distance * Math.sin(angleRadians) }; }

// --- Coordinate Conversion ---
function getCanvasCoordinates(event) { const rect = canvas.getBoundingClientRect(); let clientX, clientY; if (event.touches && event.touches.length > 0) { clientX = event.touches[0].clientX; clientY = event.touches[0].clientY; } else { clientX = event.clientX; clientY = event.clientY; } return { x: clientX - rect.left, y: clientY - rect.top }; }
function canvasToWorld(canvasX, canvasY) { return { x: (canvasX - offsetX) / scale, y: (canvasY - offsetY) / scale }; }

// --- Font and Bounds ---
function setTextContextOn(targetCtx, textObj) { const fontStyle = textObj.italic ? "italic" : "normal"; const fontWeight = textObj.bold ? "bold" : "normal"; targetCtx.font = `${fontStyle} ${fontWeight} ${textObj.fontSize}px "${textObj.fontFamily}"`; targetCtx.textAlign = "center"; targetCtx.textBaseline = "middle"; }
function calculateObjectBounds(obj) { if (!obj) return { x: 0, y: 0, width: 0, height: 0, cx: 0, cy: 0 }; if (obj.type === 'text') { if (!obj.text) return { x: 0, y: 0, width: 0, height: 0, cx: obj.x, cy: obj.y }; const tempCtx = document.createElement('canvas').getContext('2d'); setTextContextOn(tempCtx, obj); const metrics = tempCtx.measureText(obj.text.toUpperCase()); const ascent = metrics.actualBoundingBoxAscent || obj.fontSize * 0.8; const descent = metrics.actualBoundingBoxDescent || obj.fontSize * 0.2; const width = metrics.width; const height = ascent + descent; return { x: obj.x - width / 2, y: obj.y - height / 2, width: width, height: height, cx: obj.x, cy: obj.y }; } else if (obj.type === 'image') { if (!obj.image || !obj.originalWidth || !obj.originalHeight) return { x: 0, y: 0, width: 0, height: 0, cx: obj.x, cy: obj.y }; const scaledWidth = obj.originalWidth * obj.scale; const scaledHeight = obj.originalHeight * obj.scale; return { x: obj.x - scaledWidth / 2, y: obj.y - scaledHeight / 2, width: scaledWidth, height: scaledHeight, cx: obj.x, cy: obj.y }; } return { x: 0, y: 0, width: 0, height: 0, cx: 0, cy: 0 }; }
function getRotatedBoundingBox(bounds, angleDeg) { const cx = bounds.cx; const cy = bounds.cy; const w = bounds.width; const h = bounds.height; const x = bounds.x; const y = bounds.y; if (w === 0 || h === 0) return { x: cx, y: cy, width: 0, height: 0 }; const angleRad = angleDeg * Math.PI / 180; const cos = Math.cos(angleRad); const sin = Math.sin(angleRad); const corners = [ { x: x, y: y }, { x: x + w, y: y }, { x: x + w, y: y + h }, { x: x, y: y + h } ]; let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity; corners.forEach(corner => { const translatedX = corner.x - cx; const translatedY = corner.y - cy; const rotatedX = translatedX * cos - translatedY * sin; const rotatedY = translatedX * sin + translatedY * cos; const finalX = rotatedX + cx; const finalY = rotatedY + cy; minX = Math.min(minX, finalX); minY = Math.min(minY, finalY); maxX = Math.max(maxX, finalX); maxY = Math.max(maxY, finalY); }); return { x: minX, y: minY, width: maxX - minX, height: maxY - minY }; }

// --- UI Binding ---
function setControlsDisabled(wrapperElement, isDisabled, keepTextEnabled = false) {
    wrapperElement.querySelectorAll('input, select, button').forEach(el => {
        if (el.id === 'iText') {
            el.disabled = keepTextEnabled ? false : isDisabled;
        } else {
            el.disabled = isDisabled;
        }
    });
}

function handleEffectModeChange() {
    const selectedObject = selectedObjectIndex !== -1 ? canvasObjects[selectedObjectIndex] : null;
    if (selectedObject && selectedObject.type === 'text') {
        gridWarpPaddingRow.style.display = selectedObject.effectMode === 'grid-warp' ? 'flex' : 'none';
         const svg = document.getElementById('grid-warp-svg');
         if (svg) {
             svg.style.display = selectedObject.effectMode === 'grid-warp' ? 'block' : 'none';
         }
         // Deactivate mesh handler if switching away from mesh mode
         if (selectedObject.effectMode !== 'mesh' && typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler && activeMeshWarpHandler.selectedTextObject === selectedObject) {
              activeMeshWarpHandler.deactivate();
         }
         // Activate mesh handler if switching to mesh mode
         if (selectedObject.effectMode === 'mesh' && typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler && activeMeshWarpHandler.selectedTextObject !== selectedObject) {
              activeMeshWarpHandler.activate(selectedObject);
         }
    } else {
        gridWarpPaddingRow.style.display = 'none';
        const svg = document.getElementById('grid-warp-svg');
        if (svg) svg.style.display = 'none';
         // Deactivate mesh handler if no text object is selected
         if (typeof activeMeshWarpHandler !== 'undefined' && activeMeshWarpHandler) {
            activeMeshWarpHandler.deactivate();
         }
    }
    updateBodyClass(selectedObject); // Also update body class based on effect mode
}


function updateUIFromSelectedObject() {
    const selectedObject = selectedObjectIndex !== -1 ? canvasObjects[selectedObjectIndex] : null;
    handleEffectModeChange(); // Update visibility of effect-specific controls first

    const canMoveForward = selectedObjectIndex !== -1 && selectedObjectIndex < canvasObjects.length - 1;
    const canMoveBackward = selectedObjectIndex !== -1 && selectedObjectIndex > 0;

    if (selectedObject) {
        if (selectedObject.type === 'text') {
            setControlsDisabled(textControlsWrapper, false, true);
            imageControlsWrapper.classList.remove('visible');
            noImageSelectedMsg.style.display = 'block';
            deleteImageBtn.disabled = true;
            addEditTextBtn.textContent = 'Edit';
            deleteTextBtn.disabled = false;

            iText.value = selectedObject.text;
            iTextColor.value = selectedObject.color;
            iFontFamily.value = selectedObject.fontFamily;
            iBold.checked = selectedObject.bold;
            iItalic.checked = selectedObject.italic;
            iFontSize.value = selectedObject.fontSize; vFontSize.textContent = selectedObject.fontSize + 'px';
            iTextRotation.value = selectedObject.rotation; vTextRotation.textContent = selectedObject.rotation + '°';
            effectModeSelect.value = selectedObject.effectMode;
            skewSlider.value = selectedObject.skewX; vSkew.textContent = selectedObject.skewX;
            skewYSlider.value = selectedObject.skewY; vSkewY.textContent = selectedObject.skewY;
            iCurve.value = selectedObject.warpCurve; vCurve.textContent = selectedObject.warpCurve;
            iOffset.value = selectedObject.warpOffset; vOffset.textContent = selectedObject.warpOffset;
            iHeight.value = selectedObject.warpHeight; vHeight.textContent = selectedObject.warpHeight;
            iBottom.value = selectedObject.warpBottom; vBottom.textContent = selectedObject.warpBottom;
            iTriangle.checked = selectedObject.warpTriangle;
            iShiftCenter.value = selectedObject.warpShiftCenter; vShiftCenter.textContent = selectedObject.warpShiftCenter;
            iDiameter.value = selectedObject.circleDiameter; vDiameter.textContent = selectedObject.circleDiameter + 'px';
            iKerning.value = selectedObject.circleKerning; vKerning.textContent = selectedObject.circleKerning + 'px';
            iFlip.checked = selectedObject.circleFlip;
            iCurveAmount.value = selectedObject.curveAmount; vCurveAmount.textContent = selectedObject.curveAmount;
            iCurveKerning.value = selectedObject.curveKerning; vCurveKerning.textContent = selectedObject.curveKerning + 'px';
            iCurveFlip.checked = selectedObject.curveFlip;
            shadowSelect.value = selectedObject.shadowMode;
            shadowColorPicker.value = selectedObject.shadowColor;
            shadowOffsetXSlider.value = selectedObject.shadowOffsetX; vShadowOffsetX.textContent = selectedObject.shadowOffsetX + 'px';
            shadowOffsetYSlider.value = selectedObject.shadowOffsetY; vShadowOffsetY.textContent = selectedObject.shadowOffsetY + 'px';
            shadowBlurSlider.value = selectedObject.shadowBlur; vShadowBlur.textContent = selectedObject.shadowBlur + 'px';
            blockShadowColorPicker.value = selectedObject.blockShadowColor;
            blockShadowOpacitySlider.value = selectedObject.blockShadowOpacity; vBlockShadowOpacity.textContent = selectedObject.blockShadowOpacity + '%';
            blockShadowOffsetSlider.value = selectedObject.blockShadowOffset; vBlockShadowOffset.textContent = selectedObject.blockShadowOffset + 'px';
            blockShadowAngleSlider.value = selectedObject.blockShadowAngle; vBlockShadowAngle.textContent = selectedObject.blockShadowAngle + '°';
            blockShadowBlurSlider.value = selectedObject.blockShadowBlur; vBlockShadowBlur.textContent = selectedObject.blockShadowBlur + 'px';
            lineShadowColorPicker.value = selectedObject.lineShadowColor;
            lineShadowDistanceSlider.value = selectedObject.lineShadowDist; vLineShadowDistance.textContent = selectedObject.lineShadowDist + 'px';
            lineShadowAngleSlider.value = selectedObject.lineShadowAngle; vLineShadowAngle.textContent = selectedObject.lineShadowAngle + '°';
            lineShadowThicknessSlider.value = selectedObject.lineShadowThickness; vLineShadowThickness.textContent = selectedObject.lineShadowThickness + 'px';
            detailed3DPrimaryColorPicker.value = selectedObject.d3dPrimaryColor;
            detailed3DPrimaryOpacitySlider.value = selectedObject.d3dPrimaryOpacity; vDetailed3DPrimaryOpacity.textContent = selectedObject.d3dPrimaryOpacity + '%';
            detailed3DOffsetSlider.value = selectedObject.d3dOffset; vDetailed3DOffset.textContent = selectedObject.d3dOffset + 'px';
            detailed3DAngleSlider.value = selectedObject.d3dAngle; vDetailed3DAngle.textContent = selectedObject.d3dAngle + '°';
            detailed3DBlurSlider.value = selectedObject.d3dBlur; vDetailed3DBlur.textContent = selectedObject.d3dBlur + 'px';
            detailed3DSecondaryColorPicker.value = selectedObject.d3dSecondaryColor;
            detailed3DSecondaryOpacitySlider.value = selectedObject.d3dSecondaryOpacity; vDetailed3DSecondaryOpacity.textContent = selectedObject.d3dSecondaryOpacity + '%';
            detailed3DSecondaryWidthSlider.value = selectedObject.d3dSecondaryWidth; vDetailed3DSecondaryWidth.textContent = selectedObject.d3dSecondaryWidth + 'px';
            detailed3DSecondaryOffsetXSlider.value = selectedObject.d3dSecondaryOffsetX; vDetailed3DSecondaryOffsetX.textContent = selectedObject.d3dSecondaryOffsetX + 'px';
            detailed3DSecondaryOffsetYSlider.value = selectedObject.d3dSecondaryOffsetY; vDetailed3DSecondaryOffsetY.textContent = selectedObject.d3dSecondaryOffsetY + 'px';
            strokeToggle.value = selectedObject.strokeMode;
            strokeWidthSlider.value = selectedObject.strokeWidth; vStrokeWidth.textContent = selectedObject.strokeWidth + 'px';
            strokeColorPicker.value = selectedObject.strokeColor;
            linesDecorationSelect.value = selectedObject.decorationMode;
            hWeight.value = selectedObject.hLineWeight; vHWeight.textContent = selectedObject.hLineWeight + 'px';
            hDistance.value = selectedObject.hLineDist; vHDistance.textContent = selectedObject.hLineDist + 'px';
            hColor.value = selectedObject.hLineColor;
            // Update coverage slider for horizontal lines
            const hCoverage = document.getElementById('hCoverage');
            const vHCoverage = document.getElementById('hCoverageValue');
            if (hCoverage && vHCoverage) {
                hCoverage.value = selectedObject.hLineCoverage;
                vHCoverage.textContent = selectedObject.hLineCoverage + '%';
            }

            ccDistance.value = selectedObject.ccDist; vCcDistance.textContent = selectedObject.ccDist + '%';
            ccColor.value = selectedObject.ccColor; selectedObject.ccFillDir === 'top' ? ccFillTop.checked = true : ccFillBottom.checked = true;
            // Update coverage slider for color cut
            const ccCoverage = document.getElementById('ccCoverage');
            const vCcCoverage = document.getElementById('ccCoverageValue');
            if (ccCoverage && vCcCoverage) {
                ccCoverage.value = selectedObject.ccCoverage;
                vCcCoverage.textContent = selectedObject.ccCoverage + '%';
            }

            oWeight.value = selectedObject.oLineWeight; vOWeight.textContent = selectedObject.oLineWeight + 'px';
            oDistance.value = selectedObject.oLineDist; vODistance.textContent = selectedObject.oLineDist + 'px';
            oColor.value = selectedObject.oLineColor;
            // Update coverage slider for oblique lines
            const oCoverage = document.getElementById('oCoverage');
            const vOCoverage = document.getElementById('oCoverageValue');
            if (oCoverage && vOCoverage) {
                oCoverage.value = selectedObject.oCoverage;
                vOCoverage.textContent = selectedObject.oCoverage + '%';
            }

            flcDistance.value = selectedObject.flcDist; vFlcDistance.textContent = selectedObject.flcDist + '%';
            flcColor.value = selectedObject.flcColor;
            flcMaxWeight.value = selectedObject.flcWeight; vFlcMaxWeight.textContent = selectedObject.flcWeight + 'px';
            flcSpacing.value = selectedObject.flcSpacing; vFlcSpacing.textContent = selectedObject.flcSpacing + 'px'; selectedObject.flcDir === 'top' ? flcFillTop.checked = true : flcFillBottom.checked = true;
            // Update coverage slider for fading color cut
            const flcCoverage = document.getElementById('flcCoverage');
            const vFlcCoverage = document.getElementById('flcCoverageValue');
            if (flcCoverage && vFlcCoverage) {
                flcCoverage.value = selectedObject.flcCoverage;
                vFlcCoverage.textContent = selectedObject.flcCoverage + '%';
            }
            gridWarpPaddingSlider.value = selectedObject.gridPadding; vGridWarpPadding.textContent = selectedObject.gridPadding; // Update Grid Warp Padding

            // Update body class based on text object properties
            updateBodyClass(selectedObject);

        } else if (selectedObject.type === 'image') {
            setControlsDisabled(textControlsWrapper, true, true);
            imageControlsWrapper.classList.add('visible');
            noImageSelectedMsg.style.display = 'none';
            deleteImageBtn.disabled = false;
            addEditTextBtn.textContent = 'Add';
            deleteTextBtn.disabled = true;

            iImageSize.value = selectedObject.scale; vImageSize.textContent = Math.round(selectedObject.scale * 100) + '%';
            iImageRotation.value = selectedObject.rotation; vImageRotation.textContent = selectedObject.rotation + '°';

            if (selectedObject.isFromGeneration && selectedObject.generationId && !selectedObject.backgroundRemoved) {
                removeBgBtn.style.display = 'block';
                removeBgBtn.disabled = false;
                removeBgBtn.textContent = 'Remove Background';
            } else {
                removeBgBtn.style.display = 'none';
            }

            // Set body class for image selection
            document.body.className = document.body.className.replace(/ (normal|warp|skew|circle|curve|mesh|grid-warp|horizontalLines|colorCut|obliqueLines|fadingLinesCut|stroke-enabled|shadow|block-shadow|line-shadow|detailed-3d|horizontal-skew|vertical-skew|triangle-warp-enabled)/g, '') + ' image-selected';

        }
    } else {
        // No object selected
        setControlsDisabled(textControlsWrapper, true, true);
        iText.value = '';
        imageControlsWrapper.classList.remove('visible');
        noImageSelectedMsg.style.display = 'block';
        deleteImageBtn.disabled = true;
        addEditTextBtn.textContent = 'Add';
        deleteTextBtn.disabled = true;
        // Reset body class to base 'normal' state
         let currentClasses = document.body.className.split(' ');
         let baseClasses = currentClasses.filter(cls => !['normal', 'warp', 'skew', 'circle', 'curve', 'mesh', 'grid-warp', 'horizontalLines', 'colorCut', 'obliqueLines', 'fadingLinesCut', 'stroke-enabled', 'shadow', 'block-shadow', 'line-shadow', 'detailed-3d', 'horizontal-skew', 'vertical-skew', 'triangle-warp-enabled', 'image-selected'].includes(cls));
         document.body.className = [...baseClasses, 'normal'].join(' '); // Ensure 'normal' is present
    }
    // Update move button states
    moveForwardBtn.disabled = !canMoveForward;
    moveBackwardBtn.disabled = !canMoveBackward;
}

function updateSelectedObjectFromUI(property, value) {
    if (selectedObjectIndex === -1) return;
    const selectedObject = canvasObjects[selectedObjectIndex];

    if (selectedObject.type === 'text') {
        if (selectedObject.hasOwnProperty(property)) {
            // Type conversions
            const numberProps = ['fontSize', 'rotation', 'skewX', 'skewY', 'warpCurve', 'warpOffset', 'warpHeight', 'warpBottom', 'warpShiftCenter', 'circleDiameter', 'circleKerning', 'curveAmount', 'curveKerning', 'hLineWeight', 'hLineDist', 'ccDist', 'oLineWeight', 'oLineDist', 'flcDist', 'flcWeight', 'flcSpacing', 'strokeWidth', 'shadowOffsetX', 'shadowOffsetY', 'shadowBlur', 'blockShadowOpacity', 'blockShadowOffset', 'blockShadowAngle', 'blockShadowBlur', 'lineShadowDist', 'lineShadowAngle', 'lineShadowThickness', 'd3dPrimaryOpacity', 'd3dOffset', 'd3dAngle', 'd3dBlur', 'd3dSecondaryOpacity', 'd3dSecondaryWidth', 'd3dSecondaryOffsetX', 'd3dSecondaryOffsetY', 'gridPadding'];
            const boolProps = ['bold', 'italic', 'warpTriangle', 'circleFlip', 'curveFlip'];

            if (numberProps.includes(property)) {
                selectedObject[property] = parseFloat(value); // Use parseFloat for potential decimals
            } else if (boolProps.includes(property)) {
                selectedObject[property] = Boolean(value);
            } else {
                selectedObject[property] = value; // String properties
            }

            // Update corresponding value display spans
            switch (property) {
                case 'fontSize': vFontSize.textContent = Math.round(selectedObject.fontSize) + 'px'; break;
                case 'rotation': vTextRotation.textContent = Math.round(selectedObject.rotation) + '°'; break;
                case 'skewX': vSkew.textContent = Math.round(selectedObject.skewX); break;
                case 'skewY': vSkewY.textContent = Math.round(selectedObject.skewY); break;
                case 'warpCurve': vCurve.textContent = Math.round(selectedObject.warpCurve); break;
                case 'warpOffset': vOffset.textContent = Math.round(selectedObject.warpOffset); break;
                case 'warpHeight': vHeight.textContent = Math.round(selectedObject.warpHeight); break;
                case 'warpBottom': vBottom.textContent = Math.round(selectedObject.warpBottom); break;
                case 'warpShiftCenter': vShiftCenter.textContent = Math.round(selectedObject.warpShiftCenter); break;
                case 'circleDiameter': vDiameter.textContent = Math.round(selectedObject.circleDiameter) + 'px'; break;
                case 'circleKerning': vKerning.textContent = Math.round(selectedObject.circleKerning) + 'px'; break;
                case 'curveAmount': vCurveAmount.textContent = Math.round(selectedObject.curveAmount); break;
                case 'curveKerning': vCurveKerning.textContent = Math.round(selectedObject.curveKerning) + 'px'; break;
                case 'hLineWeight': vHWeight.textContent = Math.round(selectedObject.hLineWeight) + 'px'; break;
                case 'hLineDist': vHDistance.textContent = Math.round(selectedObject.hLineDist) + 'px'; break;
                case 'hLineCoverage':
                    const vHCoverage = document.getElementById('hCoverageValue');
                    if (vHCoverage) vHCoverage.textContent = Math.round(selectedObject.hLineCoverage) + '%';
                    break;
                case 'ccDist': vCcDistance.textContent = Math.round(selectedObject.ccDist) + '%'; break;
                case 'ccCoverage':
                    const vCcCoverage = document.getElementById('ccCoverageValue');
                    if (vCcCoverage) vCcCoverage.textContent = Math.round(selectedObject.ccCoverage) + '%';
                    break;
                case 'oLineWeight': vOWeight.textContent = Math.round(selectedObject.oLineWeight) + 'px'; break;
                case 'oLineDist': vODistance.textContent = Math.round(selectedObject.oLineDist) + 'px'; break;
                case 'oCoverage':
                    const vOCoverage = document.getElementById('oCoverageValue');
                    if (vOCoverage) vOCoverage.textContent = Math.round(selectedObject.oCoverage) + '%';
                    break;
                case 'flcDist': vFlcDistance.textContent = Math.round(selectedObject.flcDist) + '%'; break;
                case 'flcWeight': vFlcMaxWeight.textContent = Math.round(selectedObject.flcWeight) + 'px'; break;
                case 'flcSpacing': vFlcSpacing.textContent = Math.round(selectedObject.flcSpacing) + 'px'; break;
                case 'flcCoverage':
                    const vFlcCoverage = document.getElementById('flcCoverageValue');
                    if (vFlcCoverage) vFlcCoverage.textContent = Math.round(selectedObject.flcCoverage) + '%';
                    break;
                case 'strokeWidth': vStrokeWidth.textContent = Math.round(selectedObject.strokeWidth) + 'px'; break;
                case 'shadowOffsetX': vShadowOffsetX.textContent = Math.round(selectedObject.shadowOffsetX) + 'px'; break;
                case 'shadowOffsetY': vShadowOffsetY.textContent = Math.round(selectedObject.shadowOffsetY) + 'px'; break;
                case 'shadowBlur': vShadowBlur.textContent = Math.round(selectedObject.shadowBlur) + 'px'; break;
                case 'blockShadowOpacity': vBlockShadowOpacity.textContent = Math.round(selectedObject.blockShadowOpacity) + '%'; break;
                case 'blockShadowOffset': vBlockShadowOffset.textContent = Math.round(selectedObject.blockShadowOffset) + 'px'; break;
                case 'blockShadowAngle': vBlockShadowAngle.textContent = Math.round(selectedObject.blockShadowAngle) + '°'; break;
                case 'blockShadowBlur': vBlockShadowBlur.textContent = Math.round(selectedObject.blockShadowBlur) + 'px'; break;
                case 'lineShadowDist': vLineShadowDistance.textContent = Math.round(selectedObject.lineShadowDist) + 'px'; break;
                case 'lineShadowAngle': vLineShadowAngle.textContent = Math.round(selectedObject.lineShadowAngle) + '°'; break;
                case 'lineShadowThickness': vLineShadowThickness.textContent = Math.round(selectedObject.lineShadowThickness) + 'px'; break;
                case 'd3dPrimaryOpacity': vDetailed3DPrimaryOpacity.textContent = Math.round(selectedObject.d3dPrimaryOpacity) + '%'; break;
                case 'd3dOffset': vDetailed3DOffset.textContent = Math.round(selectedObject.d3dOffset) + 'px'; break;
                case 'd3dAngle': vDetailed3DAngle.textContent = Math.round(selectedObject.d3dAngle) + '°'; break;
                case 'd3dBlur': vDetailed3DBlur.textContent = Math.round(selectedObject.d3dBlur) + 'px'; break;
                case 'd3dSecondaryOpacity': vDetailed3DSecondaryOpacity.textContent = Math.round(selectedObject.d3dSecondaryOpacity) + '%'; break;
                case 'd3dSecondaryWidth': vDetailed3DSecondaryWidth.textContent = Math.round(selectedObject.d3dSecondaryWidth) + 'px'; break;
                case 'd3dSecondaryOffsetX': vDetailed3DSecondaryOffsetX.textContent = Math.round(selectedObject.d3dSecondaryOffsetX) + 'px'; break;
                case 'd3dSecondaryOffsetY': vDetailed3DSecondaryOffsetY.textContent = Math.round(selectedObject.d3dSecondaryOffsetY) + 'px'; break;
                 case 'gridPadding': vGridWarpPadding.textContent = Math.round(selectedObject.gridPadding); break; // Added Grid Warp Padding
            }

            // Update body class if relevant property changed
            if (['effectMode', 'decorationMode', 'strokeMode', 'shadowMode', 'warpTriangle'].includes(property)) {
                 handleEffectModeChange(); // Re-run effect mode logic (handles body class and UI visibility)
            }
            update(); // Redraw canvas
        } else {
            console.warn(`Property "${property}" not found on selected text object.`);
        }
    } else if (selectedObject.type === 'image') {
        if (property === 'scale') {
            selectedObject.scale = parseFloat(value);
            vImageSize.textContent = Math.round(selectedObject.scale * 100) + '%';
        } else if (property === 'rotation') {
            selectedObject.rotation = parseInt(value, 10);
            vImageRotation.textContent = selectedObject.rotation + '°';
        } else {
            console.warn(`Property "${property}" not found or not applicable to image object.`);
            return;
        }
        update(); // Redraw canvas
    }
}

function updateBodyClass(textObj) {
    let currentClasses = document.body.className.split(' ');
    let baseClasses = currentClasses.filter(cls => !['normal', 'warp', 'skew', 'circle', 'curve', 'mesh', 'grid-warp', 'horizontalLines', 'colorCut', 'obliqueLines', 'fadingLinesCut', 'stroke-enabled', 'shadow', 'block-shadow', 'line-shadow', 'detailed-3d', 'horizontal-skew', 'vertical-skew', 'triangle-warp-enabled', 'image-selected'].includes(cls));
    let newClasses = [];

    if (!textObj || textObj.type !== 'text') {
        // If no text object or not text, could be image or nothing selected
        if(textObj && textObj.type === 'image') {
            newClasses.push('image-selected');
        } else {
            newClasses.push('normal'); // Default if nothing selected
        }
    } else {
        // It's a text object
        const effectMode = textObj.effectMode;
        const decorationMode = textObj.decorationMode;
        const shadowMode = textObj.shadowMode;
        const strokeMode = textObj.strokeMode;
        const isTriangleWarp = textObj.warpTriangle;

        newClasses.push(effectMode); // 'normal', 'warp', 'skew', etc.

        if (decorationMode !== 'noDecoration') newClasses.push(decorationMode); // 'horizontalLines', 'colorCut', etc.
        if (strokeMode !== 'noStroke') newClasses.push('stroke-enabled'); // Use a consistent class name
        if (shadowMode !== 'noShadow') newClasses.push(shadowMode); // 'shadow', 'blockShadow', etc.

        if (effectMode === 'warp' || effectMode === 'skew') newClasses.push('horizontal-skew');
        if (effectMode === 'skew') newClasses.push('vertical-skew');
        if (effectMode === 'warp' && isTriangleWarp) newClasses.push('triangle-warp-enabled');
    }

    document.body.className = [...baseClasses, ...newClasses].join(' ');
}


// --- Layering Functions ---
function moveObjectForward() { if (selectedObjectIndex !== -1 && selectedObjectIndex < canvasObjects.length - 1) { const objToMove = canvasObjects.splice(selectedObjectIndex, 1)[0]; canvasObjects.splice(selectedObjectIndex + 1, 0, objToMove); selectedObjectIndex++; updateUIFromSelectedObject(); update(); } }
function moveObjectBackward() { if (selectedObjectIndex !== -1 && selectedObjectIndex > 0) { const objToMove = canvasObjects.splice(selectedObjectIndex, 1)[0]; canvasObjects.splice(selectedObjectIndex - 1, 0, objToMove); selectedObjectIndex--; updateUIFromSelectedObject(); update(); } }

// --- Font Preview ---
function applyFontStylesToOptions() { const selectElement = document.getElementById('iFontFamily'); if (!selectElement) return; const options = selectElement.getElementsByTagName('option'); for (let option of options) { option.style.fontFamily = `"${option.value}", sans-serif`; } } // Ensure font names with spaces are quoted

// --- Shadow/Decoration Helpers --- (Implementations assumed correct from previous steps)
function applyBlockShadow(targetCtx, textObj, x, y) { const color = textObj.blockShadowColor; const opacity = textObj.blockShadowOpacity / 100; const offset = textObj.blockShadowOffset; const angleDeg = textObj.blockShadowAngle; const blur = textObj.blockShadowBlur; const offsetCoords = calculateOffset(offset, angleDeg); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.fillStyle = hexToRgba(color, opacity); if (blur > 0) { targetCtx.shadowColor = hexToRgba(color, opacity * 0.8); targetCtx.shadowBlur = blur; targetCtx.shadowOffsetX = 0; targetCtx.shadowOffsetY = 0; } const steps = Math.max(10, Math.floor(offset / 1.5)); for (let i = steps; i >= 1; i--) { const progress = i / steps; const currentX = x + offsetCoords.x * progress; const currentY = y + offsetCoords.y * progress; if (blur > 5 && i < steps) { targetCtx.shadowColor = 'transparent'; } targetCtx.fillText((textObj.text || '').toUpperCase(), currentX, currentY); if (blur > 5 && i < steps) { targetCtx.shadowColor = hexToRgba(color, opacity * 0.8); } } targetCtx.restore(); }
function applyLineShadow(targetCtx, textObj, x, y) { const color = textObj.lineShadowColor; const distance = textObj.lineShadowDist; const angleDeg = textObj.lineShadowAngle; const thickness = Math.max(1, textObj.lineShadowThickness); const fullOffset = calculateOffset(distance, angleDeg); const cutterDistance = Math.max(0, distance - thickness); const cutterOffset = calculateOffset(cutterDistance, angleDeg); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.fillStyle = color; targetCtx.fillText((textObj.text || '').toUpperCase(), x + fullOffset.x, y + fullOffset.y); targetCtx.globalCompositeOperation = 'destination-out'; targetCtx.fillStyle = 'black'; targetCtx.fillText((textObj.text || '').toUpperCase(), x + cutterOffset.x, y + cutterOffset.y); targetCtx.restore(); }
function applyDetailed3D_ExtrusionOnly(targetCtx, textObj, x, y) { const primaryColorRgba = hexToRgba(textObj.d3dPrimaryColor, textObj.d3dPrimaryOpacity / 100); const offset = textObj.d3dOffset; const angle = textObj.d3dAngle; const blur = textObj.d3dBlur; targetCtx.save(); setTextContextOn(targetCtx, textObj); const totalOffset = calculateOffset(offset, angle); const steps = Math.max(30, Math.floor(offset)); for (let i = steps; i >= 1; i--) { const progress = i / steps; const currentOffset = { x: totalOffset.x * progress, y: totalOffset.y * progress }; targetCtx.fillStyle = primaryColorRgba; targetCtx.fillText((textObj.text || '').toUpperCase(), x + currentOffset.x, y + currentOffset.y); } if (blur > 0) { targetCtx.save(); targetCtx.shadowColor = primaryColorRgba; targetCtx.shadowBlur = blur; targetCtx.shadowOffsetX = 0; targetCtx.shadowOffsetY = 0; targetCtx.fillStyle = primaryColorRgba; targetCtx.fillText((textObj.text || '').toUpperCase(), x + totalOffset.x, y + totalOffset.y); targetCtx.restore(); } targetCtx.restore(); }
function applyDetailed3D_FrontOutline(targetCtx, textObj, x, y) { if (textObj.d3dSecondaryWidth <= 0) return; const secondaryColorRgba = hexToRgba(textObj.d3dSecondaryColor, textObj.d3dSecondaryOpacity / 100); targetCtx.save(); setTextContextOn(targetCtx, textObj); targetCtx.lineWidth = textObj.d3dSecondaryWidth; targetCtx.strokeStyle = secondaryColorRgba; targetCtx.lineJoin = 'round'; targetCtx.strokeText((textObj.text || '').toUpperCase(), x + textObj.d3dSecondaryOffsetX, y + textObj.d3dSecondaryOffsetY); targetCtx.restore(); }

// --- Master Styling Functions --- (Implementations assumed correct from previous steps)
function renderStyledObjectToOffscreen(obj, targetCtx, targetCanvasWidth, targetCanvasHeight) { /*...*/ }
function renderSingleStyledLetter(obj, letter, targetCtx, targetCanvasWidth, targetCanvasHeight) { /*...*/ } // Assume previous implementation is here

// --- Text Effect Rendering Logic --- (Implementations assumed correct from previous steps)
function drawNormalOrSkewObject(obj, targetCtx) { /*...*/ }
function drawWarpedObject(obj, targetCtx) { /*...*/ }
function drawCircularObject(obj, targetCtx) { /*...*/ }
function drawCurvedObject(obj, targetCtx) { /*...*/ }
function drawGridWarpObject(obj, targetCtx) { /*...*/ } // Assume previous implementation is here

// --- Main Drawing Logic Per Object ---
function drawTextObject(obj, targetCtx) { /*...*/ } // Assume previous implementation is here
function drawImageObject(obj, targetCtx) { /*...*/ } // Assume previous implementation is here
function drawSelectionBox(obj) { /*...*/ } // Assume previous implementation is here

// --- Main Update/Render Function ---
function update() { /*...*/ } // Assume previous implementation is here
function updateZoomDisplay() { /*...*/ } // Assume previous implementation is here

// --- Event Handlers & Listeners ---
function handleAddTextObject() {
    console.log('[AddText] Adding new text object');
    const newTextObject = createTextObject({
        text: "NEW TEXT",
        x: w / 2,
        y: h / 2,
        fontSize: 100,
        color: '#000000',
        isSelected: true
    });

    // Add to canvas objects array (this determines layer order)
    canvasObjects.push(newTextObject);
    selectedObjectIndex = canvasObjects.length - 1;

    console.log('[AddText] Text object added. Layer order:', selectedObjectIndex, 'Total objects:', canvasObjects.length);

    // Update UI and render
    updateUIFromSelectedObject();
    update();

    // Auto-focus text input for immediate editing
    if (iText) {
        setTimeout(() => iText.focus(), 100);
    }
}

function handleAddImage(file) {
    console.log('[AddImage] Adding new image object');
    const reader = new FileReader();
    reader.onload = function(e) {
        const img = new Image();
        img.onload = function() {
            const newImageObject = createImageObject(img, {
                x: w / 2,
                y: h / 2,
                scale: Math.min(300 / img.naturalWidth, 300 / img.naturalHeight, 1.0),
                isSelected: true
            });

            // Add to canvas objects array (this determines layer order)
            canvasObjects.push(newImageObject);
            selectedObjectIndex = canvasObjects.length - 1;

            console.log('[AddImage] Image object added. Layer order:', selectedObjectIndex, 'Total objects:', canvasObjects.length);

            // Update UI and render
            updateUIFromSelectedObject();
            update();
        };
        img.src = e.target.result;
    };
    reader.readAsDataURL(file);
}
function handleDeleteObject() {
    if (selectedObjectIndex !== -1 && selectedObjectIndex < canvasObjects.length) {
        console.log('[DeleteObject] Deleting object at index:', selectedObjectIndex, 'Type:', canvasObjects[selectedObjectIndex].type);

        // Remove object from array (this maintains layer order for remaining objects)
        canvasObjects.splice(selectedObjectIndex, 1);

        // Adjust selection index
        if (selectedObjectIndex >= canvasObjects.length) {
            selectedObjectIndex = canvasObjects.length - 1;
        }

        console.log('[DeleteObject] Object deleted. New selection index:', selectedObjectIndex, 'Remaining objects:', canvasObjects.length);

        // Update UI and render
        updateUIFromSelectedObject();
        update();
    }
}
function handleMouseDown(e) { /*...*/ } // Assume previous implementation is here
function handleMouseMove(e) { /*...*/ } // Assume previous implementation is here
function handleMouseUp(e) { /*...*/ } // Assume previous implementation is here
function handleMouseLeave(e) { /*...*/ } // Assume previous implementation is here
function handleWheel(e) { /*...*/ } // Assume previous implementation is here
function zoom(factor, centerOnCanvas = true) { /*...*/ } // Assume previous implementation is here

// --- Helper to get proxied URL if needed ---
function getProxiedImageUrlIfNeeded(imageUrl) { /*...*/ } // Assume previous implementation is here

// --- Load Image from URL Parameter ---
function loadImageFromUrlParam() { /*...*/ return false; } // Assume previous implementation is here

// --- Load Admin Data from URL Parameter ---
function loadAdminDataFromUrlParam() { /*...*/ return false; } // Assume previous implementation is here

// --- Load Template Data ---
async function loadTemplateData(templateId) {
    try {
        console.log('[LoadTemplate] Loading template:', templateId);
        const response = await fetch(`/api/design-templates/${templateId}`, {
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error(`Failed to load template: ${response.statusText}`);
        }

        const template = await response.json();
        console.log('[LoadTemplate] Template loaded:', template);

        // 1. Restore Admin Data
        if (template.adminData) {
            document.getElementById('adminImageUrl').value = template.adminData.imageUrl || '';
            document.getElementById('adminModel').value = template.adminData.model || '';
            document.getElementById('adminPrompt').value = template.adminData.prompt || '';
            document.getElementById('adminPalette').value = template.adminData.palette || '';

            // Store inspiration ID if available
            if (template.inspirationId) {
                document.getElementById('adminInspirationId').value = template.inspirationId;
                inspirationId = template.inspirationId;
            }
        }

        // 2. Restore Artboard
        if (template.artboard) {
            artboard = {
                x: template.artboard.x,
                y: template.artboard.y,
                width: template.artboard.width,
                height: template.artboard.height
            };
            console.log('[LoadTemplate] Artboard restored:', artboard);
        }

        // 3. Restore Canvas Objects with proper layer order
        if (template.canvasObjects && Array.isArray(template.canvasObjects)) {
            // Sort objects by layerOrder/zIndex if available, otherwise use array order
            const sortedObjects = template.canvasObjects.slice().sort((a, b) => {
                const aOrder = a.layerOrder !== undefined ? a.layerOrder : a.zIndex !== undefined ? a.zIndex : 0;
                const bOrder = b.layerOrder !== undefined ? b.layerOrder : b.zIndex !== undefined ? b.zIndex : 0;
                return aOrder - bOrder;
            });

            canvasObjects = sortedObjects.map(obj => {
                // Ensure object has required properties
                const restoredObj = {
                    ...obj,
                    id: obj.id !== undefined ? obj.id : nextId++,
                    isSelected: false // Deselect all objects on load
                };

                // Update nextId to avoid conflicts
                if (restoredObj.id >= nextId) {
                    nextId = restoredObj.id + 1;
                }

                console.log('[LoadTemplate] Restored object:', restoredObj.type, restoredObj.text || restoredObj.imageUrl, 'layerOrder:', restoredObj.layerOrder || restoredObj.zIndex || 'none');
                return restoredObj;
            });

            console.log('[LoadTemplate] Canvas objects restored. Count:', canvasObjects.length, 'NextId:', nextId);
        }

        // 4. Restore Editor State
        if (template.editorState) {
            if (template.editorState.canvasBackgroundColor) {
                canvasBackgroundColor = template.editorState.canvasBackgroundColor;
            }
            if (template.editorState.zoom) {
                scale = template.editorState.zoom.scale || 1.0;
                offsetX = template.editorState.zoom.offsetX || 0;
                offsetY = template.editorState.zoom.offsetY || 0;
            }
            if (template.editorState.nextId !== undefined) {
                nextId = Math.max(nextId, template.editorState.nextId); // Ensure no ID conflicts
            }
            console.log('[LoadTemplate] Editor state restored. Scale:', scale, 'Background:', canvasBackgroundColor);
        }

        // 5. Reset selection
        selectedObjectIndex = -1;

        // 6. Update UI and render
        updateUIFromSelectedObject();
        update();

        console.log('[LoadTemplate] Template loaded successfully');
        return true;

    } catch (error) {
        console.error('[LoadTemplate] Error loading template:', error);
        if (window.showToast) {
            window.showToast(`Error loading template: ${error.message}`, 'error');
        } else {
            alert(`Error loading template: ${error.message}`);
        }
        return false;
    }
}

// Make loadTemplateData available globally for URL parameter loading
window.loadTemplateData = loadTemplateData;

// --- Load and merge generated design data ---
async function loadGeneratedDesign(templateId, newImageUrl, userTexts) {
    try {
        console.log('[LoadGenerated] Loading generated design. Template:', templateId, 'NewImage:', newImageUrl);

        // First load the base template
        const success = await loadTemplateData(templateId);
        if (!success) {
            throw new Error('Failed to load base template');
        }

        // Update admin data with new image URL
        document.getElementById('adminImageUrl').value = newImageUrl;

        // Update text objects with user-provided texts while preserving layer order
        if (userTexts && Array.isArray(userTexts)) {
            userTexts.forEach(userText => {
                const objectIndex = canvasObjects.findIndex(obj =>
                    obj.type === 'text' && obj.id === userText.id
                );

                if (objectIndex !== -1) {
                    // Update text while preserving all other properties including layer order
                    canvasObjects[objectIndex] = {
                        ...canvasObjects[objectIndex],
                        text: userText.text,
                        isFromGeneration: true
                    };
                    console.log('[LoadGenerated] Updated text object:', userText.id, 'with text:', userText.text);
                }
            });
        }

        // Re-render with updated data
        update();

        console.log('[LoadGenerated] Generated design loaded successfully');
        return true;

    } catch (error) {
        console.error('[LoadGenerated] Error loading generated design:', error);
        if (window.showToast) {
            window.showToast(`Error loading generated design: ${error.message}`, 'error');
        } else {
            alert(`Error loading generated design: ${error.message}`);
        }
        return false;
    }
}

// --- Background Removal Handler ---
async function handleBgRemoveClick() { /*...*/ } // Assume previous implementation is here

// --- Helper function to clean objects for serialization ---
function cleanObjectForSerialization(obj) {
    // Create a clean copy without circular references
    const cleanObj = {};

    // Copy basic properties
    const basicProps = ['id', 'type', 'text', 'x', 'y', 'fontSize', 'fontFamily', 'color', 'bold', 'italic',
                       'rotation', 'scale', 'opacity', 'letterSpacing', 'effectMode', 'skewX', 'skewY'];

    basicProps.forEach(prop => {
        if (obj.hasOwnProperty(prop)) {
            cleanObj[prop] = obj[prop];
        }
    });

    // Handle image objects
    if (obj.type === 'image') {
        cleanObj.imageUrl = obj.image ? obj.image.src : obj.imageUrl;
        cleanObj.width = obj.width;
        cleanObj.height = obj.height;
        // Copy any other image-specific properties
        if (obj.strokeWidth) cleanObj.strokeWidth = obj.strokeWidth;
        if (obj.strokeColor) cleanObj.strokeColor = obj.strokeColor;
        if (obj.strokeOpacity) cleanObj.strokeOpacity = obj.strokeOpacity;
        if (obj.shadowColor) cleanObj.shadowColor = obj.shadowColor;
        if (obj.shadowOpacity) cleanObj.shadowOpacity = obj.shadowOpacity;
        if (obj.shadowOffsetX) cleanObj.shadowOffsetX = obj.shadowOffsetX;
        if (obj.shadowOffsetY) cleanObj.shadowOffsetY = obj.shadowOffsetY;
        if (obj.shadowBlur) cleanObj.shadowBlur = obj.shadowBlur;
    }

    // Handle text-specific properties
    if (obj.type === 'text') {
        // Copy text styling properties
        const textProps = ['shadow', 'shadowColor', 'shadowOffsetX', 'shadowOffsetY', 'shadowBlur',
                          'blockShadowColor', 'blockShadowOpacity', 'blockShadowOffset', 'blockShadowAngle', 'blockShadowBlur',
                          'strokeWidth', 'strokeColor', 'strokeOpacity', 'linesDecoration'];

        textProps.forEach(prop => {
            if (obj.hasOwnProperty(prop)) {
                cleanObj[prop] = obj[prop];
            }
        });

        // Handle mesh warp data carefully
        if (obj.meshWarp && obj.meshWarp.initialized) {
            cleanObj.meshWarp = {
                controlPoints: obj.meshWarp.controlPoints ? [...obj.meshWarp.controlPoints] : [],
                initialControlPoints: obj.meshWarp.initialControlPoints ? [...obj.meshWarp.initialControlPoints] : [],
                relativeControlPoints: obj.meshWarp.relativeControlPoints ? [...obj.meshWarp.relativeControlPoints] : [],
                hasCustomDistortion: obj.meshWarp.hasCustomDistortion || false,
                showGrid: obj.meshWarp.showGrid !== undefined ? obj.meshWarp.showGrid : true,
                gridRect: obj.meshWarp.gridRect ? { ...obj.meshWarp.gridRect } : null,
                initialized: true
            };
        }

        // Handle other effect properties
        if (obj.curveAmount !== undefined) cleanObj.curveAmount = obj.curveAmount;
        if (obj.curveKerning !== undefined) cleanObj.curveKerning = obj.curveKerning;
        if (obj.curveFlip !== undefined) cleanObj.curveFlip = obj.curveFlip;
        if (obj.diameter !== undefined) cleanObj.diameter = obj.diameter;
        if (obj.kerning !== undefined) cleanObj.kerning = obj.kerning;
        if (obj.flip !== undefined) cleanObj.flip = obj.flip;
    }

    // Remove any circular references or handlers
    delete cleanObj._meshWarpHandler;
    delete cleanObj.isSelected;
    delete cleanObj.image; // Remove actual Image object

    return cleanObj;
}

// --- Save Template Logic ---
async function handleSaveTemplate() {
    console.log('[SaveTemplate] Clicked');
    if (!artboard) {
        const msg = 'Cannot save template without an Artboard defined.';
        if (window.showToast) window.showToast(msg, 'warning');
        else alert(msg);
        return;
    }

    // 1. Get Admin Data
    const adminData = {
        imageUrl: document.getElementById('adminImageUrl')?.value || '',
        model: document.getElementById('adminModel')?.value || '',
        prompt: document.getElementById('adminPrompt')?.value || '',
        palette: document.getElementById('adminPalette')?.value || ''
    };
    const inspirationId = document.getElementById('adminInspirationId')?.value || null;

    // 2. Generate Preview Image
    const exportCanvas = document.createElement('canvas');
    const previewWidth = 300;
    const previewHeight = artboard.height * (previewWidth / artboard.width);
    exportCanvas.width = previewWidth;
    exportCanvas.height = previewHeight;
    const exportCtx = exportCanvas.getContext('2d');

    exportCtx.save();
    exportCtx.fillStyle = canvasBackgroundColor || '#FFFFFF'; // Use current canvas bg for preview
    exportCtx.fillRect(0, 0, previewWidth, previewHeight);
    exportCtx.scale(previewWidth / artboard.width, previewHeight / artboard.height);
    exportCtx.translate(-artboard.x, -artboard.y);

    // Draw only objects intersecting the artboard
    canvasObjects.forEach((obj) => {
        const objBounds = calculateObjectBounds(obj);
        const rotatedBounds = getRotatedBoundingBox(objBounds, obj.rotation);
        const intersects = !(
            rotatedBounds.x > artboard.x + artboard.width ||
            rotatedBounds.x + rotatedBounds.width < artboard.x ||
            rotatedBounds.y > artboard.y + artboard.height ||
            rotatedBounds.y + rotatedBounds.height < artboard.y
        );
        if (intersects) {
            // Temporarily deselect for rendering preview without selection box
            const wasSelected = obj.isSelected;
            obj.isSelected = false;
            if (obj.type === 'text') drawTextObject(obj, exportCtx);
            else if (obj.type === 'image') drawImageObject(obj, exportCtx);
            obj.isSelected = wasSelected; // Restore selection state
        }
    });
    exportCtx.restore();
    const previewDataUrl = exportCanvas.toDataURL('image/png');

    // 3. Upload Preview Image
    let previewImageUrl = '';
    const saveBtn = document.getElementById('saveTemplateBtn'); // Reference button for feedback
    if (saveBtn) saveBtn.disabled = true; // Disable button during save

    try {
        console.log('[SaveTemplate] Uploading preview image...');
        if (window.showToast) window.showToast('Uploading preview...', 'info');

        const blob = await (await fetch(previewDataUrl)).blob();
        const formData = new FormData();
        formData.append('image', blob, `template_preview_${Date.now()}.png`);

        const response = await fetch('/api/images/upload', { // Use the existing image upload endpoint
            method: 'POST',
            body: formData
             // Assuming cookie/session auth middleware handles authentication
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Preview upload failed: ${response.statusText} - ${errorText}`);
        }
        const result = await response.json();
        previewImageUrl = result.imageUrl || result.url || result.fileUrl; // Adapt based on actual API response field
        if (!previewImageUrl || typeof previewImageUrl !== 'string') {
             throw new Error('Invalid or missing imageUrl in preview upload response.');
        }
        console.log('[SaveTemplate] Preview image uploaded:', previewImageUrl);
         if (window.showToast) window.showToast('Preview uploaded, saving template...', 'info');

    } catch (e) {
        console.error('[SaveTemplate] Error uploading preview image:', e);
        const msg = `Error uploading preview image: ${e.message}`;
        if (window.showToast) window.showToast(msg, 'error'); else alert(msg);
        if (saveBtn) saveBtn.disabled = false; // Re-enable button on error
        return; // Stop if preview upload fails
    }

    // 4. Prepare Canvas Objects Data (Remove non-serializable parts and add layer order)
    const serializableObjects = canvasObjects.map((obj, index) => {
        const cleanObj = cleanObjectForSerialization(obj);
        // Add explicit layer order information (index 0 = bottom layer, higher index = top layer)
        cleanObj.layerOrder = index;
        cleanObj.zIndex = index; // Alternative property name for clarity
        console.log('[SaveTemplate] Cleaned object:', obj.type, obj.text || obj.imageUrl, 'layerOrder:', index, 'has meshWarp:', !!cleanObj.meshWarp);
        return cleanObj;
    });

    // 5. Prepare Complete Editor State
    const editorState = {
        canvasBackgroundColor: canvasBackgroundColor,
        zoom: {
            scale: scale,
            offsetX: offsetX,
            offsetY: offsetY
        },
        selectedObjectIndex: selectedObjectIndex,
        nextId: nextId,
        editorSettings: {
            // Add any additional editor settings that affect the design
            lastUpdateTimestamp: Date.now()
        }
    };

    // 6. Prepare Template Data Payload
    const templateData = {
        name: adminData.prompt.substring(0, 50) || `Template ${Date.now()}`, // Auto-name from prompt or timestamp
        inspirationId: inspirationId,
        previewImageUrl: previewImageUrl, // The URL from the upload step
        artboard: artboard ? { // Ensure artboard is not null
            x: artboard.x,
            y: artboard.y,
            width: artboard.width,
            height: artboard.height
        } : null,
        canvasObjects: serializableObjects,
        adminData: adminData,
        editorState: editorState // Include complete editor state
    };

    // Add console log to inspect the data being sent
    console.log('[SaveTemplate] Editor state being saved:', editorState);
    console.log('[SaveTemplate] Canvas objects being saved:', serializableObjects);
    console.log('[SaveTemplate] Full template data:', JSON.stringify(templateData, null, 2));

    // 7. Send Data to Backend
    try {
        console.log('[SaveTemplate] Saving template data to /api/design-templates');
        const saveResponse = await fetch('/api/design-templates', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(templateData)
            // Assuming credentials (cookies) are handled automatically by the browser or fetch defaults
        });

        if (!saveResponse.ok) {
             const errorText = await saveResponse.text();
             throw new Error(`Save template failed: ${saveResponse.statusText} - ${errorText}`);
        }

        const savedTemplate = await saveResponse.json();
        console.log('[SaveTemplate] Template saved successfully:', savedTemplate);
        const msg = 'Template saved successfully!';
         if (window.showToast) window.showToast(msg, 'success'); else alert(msg);

    } catch (e) {
         console.error('[SaveTemplate] Error saving template data:', e);
         const msg = `Error saving template: ${e.message}`;
         if (window.showToast) window.showToast(msg, 'error'); else alert(msg);
    } finally {
         if (saveBtn) saveBtn.disabled = false; // Re-enable button regardless of success/failure
    }
}

// --- Save Text Style Logic ---
async function handleSaveTextStyle() {
    console.log('[SaveTextStyle] Button clicked - starting save process');

    if (!artboard) {
        const msg = 'Cannot save text style without an Artboard defined.';
        console.error('[SaveTextStyle] Error:', msg);
        if (window.showToast) window.showToast(msg, 'warning');
        else alert(msg);
        return;
    }

    console.log('[SaveTextStyle] Artboard found:', artboard);
    console.log('[SaveTextStyle] Canvas objects count:', canvasObjects ? canvasObjects.length : 0);

    const saveBtn = saveTextStyleBtn;
    if (saveBtn) saveBtn.disabled = true; // Disable button to prevent double-clicks

    // 1. Generate Preview Image (same as template)
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = artboard.width;
    tempCanvas.height = artboard.height;
    const tempCtx = tempCanvas.getContext('2d');

    // Set white background for preview
    tempCtx.fillStyle = '#ffffff';
    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

    // Draw all objects within artboard bounds
    canvasObjects.forEach(obj => {
        if (obj.x >= artboard.x && obj.y >= artboard.y &&
            obj.x <= artboard.x + artboard.width && obj.y <= artboard.y + artboard.height) {

            const relativeX = obj.x - artboard.x;
            const relativeY = obj.y - artboard.y;

            if (obj.type === 'text') {
                drawTextObject(tempCtx, { ...obj, x: relativeX, y: relativeY });
            } else if (obj.type === 'image') {
                drawImageObject(tempCtx, { ...obj, x: relativeX, y: relativeY });
            }
        }
    });

    const previewDataUrl = tempCanvas.toDataURL('image/png');
    let previewImageUrl = '';

    // 2. Upload Preview Image
    try {
        console.log('[SaveTextStyle] Uploading preview image...');
        if (window.showToast) window.showToast('Uploading preview...', 'info');

        const blob = await (await fetch(previewDataUrl)).blob();
        const formData = new FormData();
        formData.append('image', blob, `text_style_preview_${Date.now()}.png`);

        const response = await fetch('/api/images/upload', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Preview upload failed: ${response.statusText} - ${errorText}`);
        }

        const uploadResult = await response.json();
        previewImageUrl = uploadResult.url;
        console.log('[SaveTextStyle] Preview uploaded successfully:', previewImageUrl);

    } catch (e) {
        console.error('[SaveTextStyle] Error uploading preview:', e);
        const msg = `Error uploading preview: ${e.message}`;
        if (window.showToast) window.showToast(msg, 'error'); else alert(msg);
        if (saveBtn) saveBtn.disabled = false;
        return;
    }

    // 3. Prepare Canvas Objects (filter to artboard bounds)
    const filteredObjects = canvasObjects.filter(obj => {
        return obj.x >= artboard.x && obj.y >= artboard.y &&
               obj.x <= artboard.x + artboard.width && obj.y <= artboard.y + artboard.height;
    });

    const serializableObjects = filteredObjects.map((obj, index) => {
        const cleanObj = cleanObjectForSerialization(obj);

        // Convert to relative coordinates
        cleanObj.x = obj.x - artboard.x;
        cleanObj.y = obj.y - artboard.y;

        // Add explicit layer order information for objects within artboard
        cleanObj.layerOrder = index;
        cleanObj.zIndex = index;

        console.log('[SaveTextStyle] Cleaned object:', obj.type, obj.text || obj.imageUrl, 'layerOrder:', index, 'has meshWarp:', !!cleanObj.meshWarp);
        return cleanObj;
    });

    // 4. Get name from user
    const styleName = prompt('Enter a name for this text style:');
    if (!styleName) {
        console.log('[SaveTextStyle] User cancelled name input');
        if (saveBtn) saveBtn.disabled = false;
        return;
    }

    // 5. Prepare Text Style Data Payload (simplified compared to template)
    const textStyleData = {
        name: styleName.trim() || `Text Style ${Date.now()}`,
        previewImageUrl: previewImageUrl,
        artboard: {
            x: 0, // Always start at 0,0 for text styles
            y: 0,
            width: artboard.width,
            height: artboard.height
        },
        canvasObjects: serializableObjects
    };

    console.log('[SaveTextStyle] Data being sent:', JSON.stringify(textStyleData, null, 2));

    // 6. Send Data to Backend
    try {
        console.log('[SaveTextStyle] Saving text style data to /api/text-styles');
        const saveResponse = await fetch('/api/text-styles', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(textStyleData)
        });

        if (!saveResponse.ok) {
             const errorText = await saveResponse.text();
             throw new Error(`Save text style failed: ${saveResponse.statusText} - ${errorText}`);
        }

        const savedTextStyle = await saveResponse.json();
        console.log('[SaveTextStyle] Text style saved successfully:', savedTextStyle);
        const msg = `Text style "${styleName}" saved successfully!`;
         if (window.showToast) window.showToast(msg, 'success'); else alert(msg);

    } catch (e) {
         console.error('[SaveTextStyle] Error saving text style data:', e);
         const msg = `Error saving text style: ${e.message}`;
         if (window.showToast) window.showToast(msg, 'error'); else alert(msg);
    } finally {
         if (saveBtn) saveBtn.disabled = false; // Re-enable button regardless of success/failure
    }
}

// --- Initial State ---
function initialize() {
    scale = Math.min(canvas.clientWidth / w, canvas.clientHeight / h) * 0.8;
    offsetX = (canvas.clientWidth - w * scale) / 2;
    offsetY = (canvas.clientHeight - h * scale) / 2;
    const initialObject = createTextObject({
        text: "DESIGN",
        isSelected: false,
        x: w / 2 - 150,
        y: h / 2,
        fontSize: 200,
        color: '#3b82f6'
    });
    canvasObjects.push(initialObject);
    selectedObjectIndex = -1;
    artboard = null; // Ensure artboard is null initially
    applyFontStylesToOptions();
    updateUIFromSelectedObject();
    update();
    console.log("Default editor initialized.");
}

// --- DOMContentLoaded Listener ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log("DOM fully loaded and parsed");

    // Initialize Pickr for background color
    function initializePickr() {
        const colorPickerElement = document.getElementById('canvasBgColorPicker');
        if (!colorPickerElement) {
            console.error('Canvas background color picker element not found');
            return;
        }
        // Ensure Pickr library is loaded (it's included via script tag)
        if (typeof Pickr === 'undefined') {
            console.error('Pickr library not loaded.');
            return;
        }
        try {
            const pickr = Pickr.create({
                el: colorPickerElement,
                theme: 'nano', // or 'classic', 'monolith'
                default: canvasBackgroundColor, // Use the global variable
                swatches: [
                    '#ffffff', '#f4f7fc', '#d1d5db', '#9ca3af', '#6b7280', '#4b5563', '#374151', '#1f2937', '#111827', '#000000',
                    '#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e', '#14b8a6', '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899'
                ],
                components: {
                    preview: true,
                    opacity: true,
                    hue: true,
                    interaction: {
                        hex: true,
                        rgba: true,
                        input: true,
                        clear: false,
                        save: true
                    }
                }
            });

            pickr.on('save', (color, instance) => {
                const newColor = color.toHEXA().toString();
                console.log('Pickr save event:', newColor);
                canvasBackgroundColor = newColor; // Update the global variable
                update(); // Redraw canvas with new background
                pickr.hide();
            });

            console.log('Pickr initialized successfully');
        } catch (error) {
            console.error('Error initializing Pickr:', error);
        }
    }
    initializePickr(); // Call initialization


    // --- Loading Logic ---
    const params = new URLSearchParams(window.location.search);
    const templateId = params.get('templateId');
    const source = params.get('source');
    let loadedSomething = false;

    if (source === 'generation' && templateId) {
        console.log('[EditorLoad] Source is generation, attempting to load merged data.');
        const newImageUrl = sessionStorage.getItem('generatedImageUrl');
        const userTextsRaw = sessionStorage.getItem('userTexts');
        const originalTemplateId = sessionStorage.getItem('originalTemplateId');

        if (newImageUrl && userTextsRaw && originalTemplateId === templateId) {
            try {
                const userTexts = JSON.parse(userTextsRaw);
                loadedSomething = await loadGeneratedDesign(templateId, newImageUrl, userTexts);
                // Clear session storage after successful load
                if (loadedSomething) {
                     sessionStorage.removeItem('generatedImageUrl');
                     sessionStorage.removeItem('userTexts');
                     sessionStorage.removeItem('originalTemplateId');
                     sessionStorage.removeItem('generationId');
                }
            } catch (e) {
                console.error("Error parsing sessionStorage data or loading generated design:", e);
                if (window.showToast) window.showToast("Error loading generated design data.", 'error');
                else alert("Error loading generated design data. Initializing default editor.");
            }
        } else {
            console.warn("[EditorLoad] Missing data in sessionStorage for generated design. Loading original template instead.");
            loadedSomething = await loadTemplateData(templateId); // Fallback to original template
        }

    } else if (templateId) {
        console.log('[EditorLoad] Loading template directly:', templateId);
        loadedSomething = await loadTemplateData(templateId);
    }

    // Load from image/admin params ONLY if nothing else loaded yet
    if (!loadedSomething) {
        console.log('[EditorLoad] No template/generation loaded, checking for image/admin params...');
         loadedSomething = loadImageFromUrlParam(); // Returns true if image loaded
         loadAdminDataFromUrlParam(); // Load admin data regardless of image load
    }

    // Initialize default state ONLY if nothing was loaded by any method
    if (!loadedSomething) {
        console.log('[EditorLoad] No template, generation, or image param loaded. Initializing default state.');
        initialize();
    }


    // --- Attach Event Listeners ---

    // Text Controls
    iText.oninput = () => { if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex].type === 'text') { updateSelectedObjectFromUI('text', iText.value); } };
    addEditTextBtn.onclick = () => { if (selectedObjectIndex !== -1 && canvasObjects[selectedObjectIndex].type === 'text') { iText.focus(); } else { handleAddTextObject(); } };
    deleteTextBtn.onclick = handleDeleteObject;
    iTextColor.oninput = (e) => updateSelectedObjectFromUI('color', e.target.value);
    iFontFamily.onchange = (e) => updateSelectedObjectFromUI('fontFamily', e.target.value);
    iBold.onchange = (e) => updateSelectedObjectFromUI('bold', e.target.checked);
    iItalic.onchange = (e) => updateSelectedObjectFromUI('italic', e.target.checked);
    iFontSize.oninput = (e) => updateSelectedObjectFromUI('fontSize', e.target.value);
    iTextRotation.oninput = (e) => updateSelectedObjectFromUI('rotation', e.target.value);
    effectModeSelect.onchange = (e) => updateSelectedObjectFromUI('effectMode', e.target.value);
    skewSlider.oninput = (e) => updateSelectedObjectFromUI('skewX', e.target.value);
    skewYSlider.oninput = (e) => updateSelectedObjectFromUI('skewY', e.target.value);
    iCurve.oninput = (e) => updateSelectedObjectFromUI('warpCurve', e.target.value);
    iOffset.oninput = (e) => updateSelectedObjectFromUI('warpOffset', e.target.value);
    iHeight.oninput = (e) => updateSelectedObjectFromUI('warpHeight', e.target.value);
    iBottom.oninput = (e) => updateSelectedObjectFromUI('warpBottom', e.target.value);
    iTriangle.onchange = (e) => updateSelectedObjectFromUI('warpTriangle', e.target.checked);
    iShiftCenter.oninput = (e) => updateSelectedObjectFromUI('warpShiftCenter', e.target.value);
    iDiameter.oninput = (e) => updateSelectedObjectFromUI('circleDiameter', e.target.value);
    iKerning.oninput = (e) => updateSelectedObjectFromUI('circleKerning', e.target.value);
    iFlip.onchange = (e) => updateSelectedObjectFromUI('circleFlip', e.target.checked);
    iCurveAmount.oninput = (e) => updateSelectedObjectFromUI('curveAmount', e.target.value);
    iCurveKerning.oninput = (e) => updateSelectedObjectFromUI('curveKerning', e.target.value);
    iCurveFlip.onchange = (e) => updateSelectedObjectFromUI('curveFlip', e.target.checked);
    shadowSelect.onchange = (e) => updateSelectedObjectFromUI('shadowMode', e.target.value);
    shadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('shadowColor', e.target.value);
    shadowOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetX', e.target.value);
    shadowOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('shadowOffsetY', e.target.value);
    shadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('shadowBlur', e.target.value);
    blockShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('blockShadowColor', e.target.value);
    blockShadowOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowOpacity', e.target.value);
    blockShadowOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowOffset', e.target.value);
    blockShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowAngle', e.target.value);
    blockShadowBlurSlider.oninput = (e) => updateSelectedObjectFromUI('blockShadowBlur', e.target.value);
    lineShadowColorPicker.oninput = (e) => updateSelectedObjectFromUI('lineShadowColor', e.target.value);
    lineShadowDistanceSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowDist', e.target.value);
    lineShadowAngleSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowAngle', e.target.value);
    lineShadowThicknessSlider.oninput = (e) => updateSelectedObjectFromUI('lineShadowThickness', e.target.value);
    detailed3DPrimaryColorPicker.oninput = (e) => updateSelectedObjectFromUI('d3dPrimaryColor', e.target.value);
    detailed3DPrimaryOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('d3dPrimaryOpacity', e.target.value);
    detailed3DOffsetSlider.oninput = (e) => updateSelectedObjectFromUI('d3dOffset', e.target.value);
    detailed3DAngleSlider.oninput = (e) => updateSelectedObjectFromUI('d3dAngle', e.target.value);
    detailed3DBlurSlider.oninput = (e) => updateSelectedObjectFromUI('d3dBlur', e.target.value);
    detailed3DSecondaryColorPicker.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryColor', e.target.value);
    detailed3DSecondaryOpacitySlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOpacity', e.target.value);
    detailed3DSecondaryWidthSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryWidth', e.target.value);
    detailed3DSecondaryOffsetXSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOffsetX', e.target.value);
    detailed3DSecondaryOffsetYSlider.oninput = (e) => updateSelectedObjectFromUI('d3dSecondaryOffsetY', e.target.value);
    strokeToggle.onchange = (e) => updateSelectedObjectFromUI('strokeMode', e.target.value);
    strokeWidthSlider.oninput = (e) => updateSelectedObjectFromUI('strokeWidth', e.target.value);
    strokeColorPicker.oninput = (e) => updateSelectedObjectFromUI('strokeColor', e.target.value);
    linesDecorationSelect.onchange = (e) => updateSelectedObjectFromUI('decorationMode', e.target.value);
    hWeight.oninput = (e) => updateSelectedObjectFromUI('hLineWeight', e.target.value);
    hDistance.oninput = (e) => updateSelectedObjectFromUI('hLineDist', e.target.value);
    hColor.oninput = (e) => updateSelectedObjectFromUI('hLineColor', e.target.value);
    // Add coverage slider for horizontal lines
    const hCoverage = document.getElementById('hCoverage');
    if (hCoverage) hCoverage.oninput = (e) => updateSelectedObjectFromUI('hLineCoverage', e.target.value);

    ccDistance.oninput = (e) => updateSelectedObjectFromUI('ccDist', e.target.value);
    ccColor.oninput = (e) => updateSelectedObjectFromUI('ccColor', e.target.value);
    ccFillTop.onchange = () => updateSelectedObjectFromUI('ccFillDir', 'top');
    ccFillBottom.onchange = () => updateSelectedObjectFromUI('ccFillDir', 'bottom');
    // Add coverage slider for color cut
    const ccCoverage = document.getElementById('ccCoverage');
    if (ccCoverage) ccCoverage.oninput = (e) => updateSelectedObjectFromUI('ccCoverage', e.target.value);

    oWeight.oninput = (e) => updateSelectedObjectFromUI('oLineWeight', e.target.value);
    oDistance.oninput = (e) => updateSelectedObjectFromUI('oLineDist', e.target.value);
    oColor.oninput = (e) => updateSelectedObjectFromUI('oLineColor', e.target.value);
    // Add coverage slider for oblique lines
    const oCoverage = document.getElementById('oCoverage');
    if (oCoverage) oCoverage.oninput = (e) => updateSelectedObjectFromUI('oCoverage', e.target.value);

    flcDistance.oninput = (e) => updateSelectedObjectFromUI('flcDist', e.target.value);
    flcColor.oninput = (e) => updateSelectedObjectFromUI('flcColor', e.target.value);
    flcMaxWeight.oninput = (e) => updateSelectedObjectFromUI('flcWeight', e.target.value);
    flcSpacing.oninput = (e) => updateSelectedObjectFromUI('flcSpacing', e.target.value);
    flcFillTop.onchange = () => updateSelectedObjectFromUI('flcDir', 'top');
    flcFillBottom.onchange = () => updateSelectedObjectFromUI('flcDir', 'bottom');
    // Add coverage slider for fading color cut
    const flcCoverage = document.getElementById('flcCoverage');
    if (flcCoverage) flcCoverage.oninput = (e) => updateSelectedObjectFromUI('flcCoverage', e.target.value);
     gridWarpPaddingSlider.oninput = (e) => updateSelectedObjectFromUI('gridPadding', e.target.value); // Added Grid Warp Padding

    // Image Controls
    addImageBtn.onclick = () => imageFileInput.click();
    imageFileInput.onchange = (e) => { if (e.target.files && e.target.files[0]) { handleAddImage(e.target.files[0]); } e.target.value = null; };
    deleteImageBtn.onclick = handleDeleteObject;
    iImageSize.oninput = (e) => updateSelectedObjectFromUI('scale', e.target.value);
    iImageRotation.oninput = (e) => updateSelectedObjectFromUI('rotation', e.target.value);
    removeBgBtn.addEventListener('click', handleBgRemoveClick);

    // Canvas Interaction
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('wheel', handleWheel, { passive: false }); // Keep passive false for preventDefault
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    canvasArea.addEventListener('mouseleave', handleMouseLeave); // Attach to canvasArea

    // Zoom Buttons
    zoomInBtn.addEventListener('click', () => zoom(1.2));
    zoomOutBtn.addEventListener('click', () => zoom(1 / 1.2));

    // Layering Buttons
    moveForwardBtn.addEventListener('click', moveObjectForward);
    moveBackwardBtn.addEventListener('click', moveObjectBackward);

    // Artboard Button
    toggleArtboardBtn.addEventListener('click', () => {
        if (!artboard) {
            // Create centered artboard with default size if none exists
            const viewCenterX = canvas.clientWidth / 2;
            const viewCenterY = canvas.clientHeight / 2;
            const worldCenter = canvasToWorld(viewCenterX, viewCenterY);
            artboard = {
                x: worldCenter.x - 300, // Center based on current view
                y: worldCenter.y - 300,
                width: 600,
                height: 600,
                isSelected: true
            };
        } else {
            // Toggle selection mode
            artboard.isSelected = !artboard.isSelected;
        }
        // Deselect any canvas object when toggling artboard selection
        if (artboard.isSelected && selectedObjectIndex !== -1) {
            canvasObjects[selectedObjectIndex].isSelected = false;
            selectedObjectIndex = -1;
            updateUIFromSelectedObject(); // Update UI to reflect no object selected
        }
        update(); // Redraw with updated artboard state
    });


    // Sidebar Tabs
    sidebarTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetContentId = tab.getAttribute('data-tab');
            sidebarTabs.forEach(t => t.classList.remove('active'));
            sidebarContents.forEach(c => c.classList.remove('active'));
            tab.classList.add('active');
            document.getElementById(targetContentId)?.classList.add('active');
        });
    });

    // Text Property Tabs
    propertyTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetPanelClass = tab.getAttribute('data-panel');
            propertyTabs.forEach(t => t.classList.remove('active'));
            propertyPanels.forEach(p => p.classList.remove('active'));
            tab.classList.add('active');
            // Find the panel within the text controls wrapper
            document.querySelector(`#text-controls .property-panel.${targetPanelClass}`)?.classList.add('active');
        });
    });

    // Save Template Button
    if (saveTemplateBtn) {
        saveTemplateBtn.addEventListener('click', handleSaveTemplate);
    } else {
        console.error('Save Template button not found!');
    }

    // Save Text Style Button
    if (saveTextStyleBtn) {
        console.log('[Init] Save Text Style button found, adding event listener');
        saveTextStyleBtn.addEventListener('click', handleSaveTextStyle);
    } else {
        console.error('Save Text Style button not found!');
    }

}); // End DOMContentLoaded